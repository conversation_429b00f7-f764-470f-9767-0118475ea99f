<template>
    <div class="p-4 lg:gap-6 lg:p-6">

        <Head title="Profile" />
        <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
        <div class="flex justify-start items-center mb-4 max-w-3xl gap-4 flex-wrap">
            <h1 class="text-2xl font-bold">
                Profile
            </h1>
        </div>
        <div class="mb-6">
            <Tabs default-value="account">
                <div class="w-full overflow-x-auto">
                    <TabsList class="flex items-center !justify-start w-fit gap-1">
                        <TabsTrigger value="account"> Account </TabsTrigger>
                        <TabsTrigger value="password"> Password </TabsTrigger>
                    </TabsList>
                </div>
                <TabsContent value="account" class="data-[state=active]:shadow-none text-primary">
                    <div class="w-full max-w-sm">
                        <Card>
                            <CardContent class="p-0">
                                <div class="p-4">
                                    <form @submit.prevent="updateAccount">
                                        <div class="grid gap-5 mb-5">
                                            <div>
                                                <TextInput 
                                                    v-model="accountForm.name"
                                                    :error="accountForm.errors.name"
                                                    label="Name" 
                                                />
                                            </div>
                                            <div>
                                                <TextInput 
                                                    v-model="accountForm.email"
                                                    :error="accountForm.errors.email"
                                                    label="Email" 
                                                />
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-end gap-4 flex-wrap">
                                            <Button 
                                                :disabled="accountForm.processing"
                                                label="Save Changes" 
                                                type="submit" 
                                            />
                                        </div>
                                    </form>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>
                <TabsContent value="password" class="data-[state=active]:shadow-none text-primary">
                    <div class="w-full max-w-sm">
                        <Card>
                            <CardContent class="p-0">
                                <div class="p-4">
                                    <form @submit.prevent="updatePassword">
                                        <div class="grid gap-5 mb-5">
                                            <div>
                                                <p class="leading-snug text-sm text-[#64748B]">Make changes to your password here. Click save when you're done.</p>
                                            </div>
                                            <div>
                                                <TextInput 
                                                    v-model="passwordForm.current_password"
                                                    :error="passwordForm.errors.current_password"
                                                    label="Current Password" 
                                                    type="password" 
                                                />
                                            </div>
                                            <div>
                                                <TextInput 
                                                    v-model="passwordForm.password"
                                                    :error="passwordForm.errors.password"
                                                    label="New Password" 
                                                    type="password" 
                                                />
                                            </div>
                                            <div>
                                                <TextInput 
                                                    v-model="passwordForm.password_confirmation"
                                                    label="Confirm New Password" 
                                                    type="password" 
                                                />
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-end gap-4 flex-wrap">
                                            <Button 
                                                :disabled="passwordForm.processing"
                                                label="Save Changes" 
                                                type="submit" 
                                            />
                                        </div>
                                    </form>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>
            </Tabs>
        </div>
    </div>
</template>

<script>
import Layout from '@/Shared/Layout.vue';
import { Head } from '@inertiajs/vue3';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import {
  TabsContent,
} from '@/Components/ui/tabs';
import {
  TableRow
} from '@/Components/ui/table';
import { TableCell, TableHead, TableHeader, TableBody, Table } from '@/Shared/table';
import DisplayLabel from '@/Shared/DisplayLabel.vue';
import { TabsTrigger, TabsList, Tabs } from '@/Shared/tabs';
import { Card, CardContent } from '@/Components/ui/card';
import TextInput from '@/Shared/TextInput.vue';
import Button from '@/Shared/Button.vue';
import { route } from 'ziggy-js';

export default {
    layout: Layout,
    components: {
        Head,
        Breadcrumb,
        TabsContent,
        TableRow,
        TableCell,
        TableHead,
        TableHeader,
        TableBody,
        Table,
        DisplayLabel,
        TabsTrigger,
        TabsList,
        Tabs,
        Card,
        CardContent,
        TextInput,
        Button,
    },
    props: {
        user: Object,
    },
    data() {
        return {
            accountForm: this.$inertia.form({
                name: this.user.name,
                email: this.user.email,
            }),
            passwordForm: this.$inertia.form({
                current_password: '',
                password: '',
                password_confirmation: '',
            }),
            breadcrumbs: [
                { name: 'Dashboard', link: route('dashboard') },
                { name: 'Profile', link: route('profile'), is_active: true },
            ],
        }
    },
    methods: {
        updateAccount() {
            this.accountForm.put(route('profile.update-account'), {
                preserveScroll: true,
                onSuccess: () => {
                    // Optional: Show success message
                },
            });
        },
        updatePassword() {
            this.passwordForm.put(route('profile.update-password'), {
                preserveScroll: true,
                onSuccess: () => {
                    this.passwordForm.reset();
                    // Optional: Show success message
                },
            });
        },
    },
}
</script>