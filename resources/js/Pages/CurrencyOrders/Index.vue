<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head title="Currency Orders" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <Card>
      <CardContent class="p-0">
        <div class="p-4">
          <SearchFilter @reset="reset">
            <div>
              <Label class="mb-2 inline-block">Date Range:</Label>
              <Popover>
                <PopoverTrigger as-child>
                  <UIButton variant="outline"
                    :class="`w-full justify-start text-left font-normal shadow-none min-h-10 overflow-hidden ${!value.start ? 'text-muted-foreground' : ''}`">
                    <CalendarIcon class="mr-2 h-4 w-4" />
                    {{ formattedDateResult }}
                  </UIButton>
                </PopoverTrigger>
                <PopoverContent class="w-auto p-0">
                  <RangeCalendar v-model="value" initial-focus :number-of-months="2"
                    @update:model-value="handleDateChange" />
                </PopoverContent>
              </Popover>
            </div>
            <div>
              <Label class="mb-2 inline-block">Reference:</Label>
              <Input v-model="form.reference" type="text" name="reference" placeholder="Reference" autocomplete="off"
                :value="form.reference" />
            </div>
            <div>
              <Label class="mb-2 inline-block">Currency In:</Label>
              <Input v-model="form.currency_in" type="text" name="currency_in" placeholder="Currency In"
                autocomplete="off" />
            </div>
            <div>
              <Label class="mb-2 inline-block">Currency Out:</Label>
              <Input v-model="form.currency_out" type="text" name="currency_out" placeholder="Currency Out"
                autocomplete="off" />
            </div>
            <div>
              <Label class="mb-2 inline-block">Customer:</Label>
              <Input v-model="form.customer" type="text" name="customer" placeholder="Customer" autocomplete="off" />
            </div>
            <div>
              <Label class="mb-2 inline-block">Created By:</Label>
              <Input v-model="form.created_by" type="text" name="created_by" placeholder="Created by"
                autocomplete="off" />
            </div>
            <div>
              <Label class="mb-2 inline-block">Currency Order Type:</Label>
              <Select v-model="form.currency_order_type" class="form-select mt-1 w-full">
                <SelectTrigger>
                  <SelectValue placeholder="Select Order Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem v-for="type in computedCurrencyOrderTypes" :key="type.id" :value="type.value.toString()">
                      {{ type.label }}</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label class="mb-2 inline-block">Status:</Label>
              <Select v-model="form.status" class="form-select mt-1 w-full">
                <SelectTrigger>
                  <SelectValue placeholder="Select Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem v-for="status in statusOptions" :key="status.value" :value="status.value">
                      {{ status.label }}
                    </SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <template #actions>
              <Button type="button" label="Search" @click="search" class="cursor-pointer" />
            </template>
          </SearchFilter>
        </div>
      </CardContent>
    </Card>
    <div>
      <Separator class="my-5" />
    </div>
    <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
      <h1 class="text-2xl font-bold">Currency Order Overview</h1>
      <div>
        <Dialog v-model:open="createDialogIsOpen">
          <div class="flex gap-2">
            <form v-if="canExportCurrencyOrder" :action="route('currency-orders.export')" method="get" class="inline-flex">
              <input v-if="form.reference" type="hidden" name="reference" :value="form.reference">
              <input v-if="form.start_date" type="hidden" name="start_date" :value="form.start_date">
              <input v-if="form.end_date" type="hidden" name="end_date" :value="form.end_date">
              <input v-if="form.currency_in" type="hidden" name="currency_in" :value="form.currency_in">
              <input v-if="form.currency_out" type="hidden" name="currency_out" :value="form.currency_out">
              <input v-if="form.customer" type="hidden" name="customer" :value="form.customer">
              <input v-if="form.created_by" type="hidden" name="created_by" :value="form.created_by">
              <input v-if="form.currency_order_type" type="hidden" name="currency_order_type"
                :value="form.currency_order_type">
              <input v-if="form.status" type="hidden" name="status" :value="form.status">
              <input type="hidden" name="sort" :value="typeof form.sort === 'function' ? 'created_at' : form.sort">
              <input type="hidden" name="direction" :value="form.direction">
              <Button type="submit" label="Export CSV" class="cursor-pointer" />
            </form>
            <Button
              v-if="canCreateCurrencyOrder"
              label="Create"
              icon="plus"
              class="cursor-pointer"
              @click="createCurrencyOrder"
            />
          </div>
          <DialogContent class="sm:max-w-[425px]">
            <DialogHeader class="px-2">
              <DialogTitle>Create Currency Order</DialogTitle>
              <DialogDescription></DialogDescription>
            </DialogHeader>
            <form @submit.prevent="submitCreateCurrencyOrder">
              <div class="overflow-y-auto mb-4 p-2">
                <div class="grid gap-4 max-h-[75vh]">
                  <div class="grid gap-2">
                    <Label>Order Type:</Label>
                    <Select @update:modelValue="handleTypeChange" :value="dialog.currency_order_type_id"
                      class="form-select mt-1 w-full">
                      <SelectTrigger>
                        <SelectValue placeholder="Select Order Type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          <SelectItem v-for="type in computedCurrencyOrderTypes" :key="type.id" :value="type.value.toString()">
                            {{ type.label }}</SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                    <span v-if="errors.currency_order_type_id" class="text-red-600 text-sm">{{
                      errors.currency_order_type_id }}</span>
                  </div>
                  <SelectInput v-if="isFieldVisible('customer')" v-model="customerSelectIsOpen"
                    :error="errors.customer_id" label="Customer" :option-values="customerOptionValues"
                    :value="dialog.customer_id" popover-content-class="w-[359px] max-w-[calc(100vw-4rem)]"
                    :disabled="['e', 'r'].includes(dialog.currency_order_type_id)">
                    <template #selected>
                      <div class="flex items-center gap-2">
                        <Avatar v-if="selectedCustomerOptionValue?.photo" class="size-6">
                          <AvatarImage :src="selectedCustomerOptionValue?.photo" alt="Agent Photo" />
                          <AvatarFallback>{{ selectedCustomerOptionValue?.name }}</AvatarFallback>
                        </Avatar>
                        <span>{{ dialog.customer_id ? selectedCustomerOptionValue?.value : `Select Customer` }}</span>
                      </div>
                    </template>
                    <CommandItem v-for="option in customerOptionValues" :key="option.value" :value="option.value"
                      @select="
                        (ev) => {
                          if (typeof ev.detail.value === 'string') {
                            dialog.customer_id = ev.detail.value
                          }
                          dialog.customer_id = option.id.toString()
                          customerSelectIsOpen = false
                        }
                      ">
                      <div class="flex items-center gap-2">
                        <Avatar v-if="option.photo" class="size-6">
                          <AvatarImage :src="option.photo" alt="Agent Photo" />
                          <AvatarFallback>{{ option.name }}</AvatarFallback>
                        </Avatar>
                        <span>{{ option.value }}</span>
                      </div>
                      <Check class="ml-auto h-4 w-4"
                        :class="[dialog.customer_id === option.id.toString() ? 'opacity-100' : 'opacity-0']" />
                    </CommandItem>
                  </SelectInput>
                  <SelectInput v-if="isFieldVisible('in_currency_id')" v-model="inCurrencySelectIsOpen"
                    :error="errors.in_currency_id" label="Currency In" :option-values="inCurrencyOptionValues"
                    :value="dialog.in_currency_id" popover-content-class="w-[359px] max-w-[calc(100vw-4rem)]">
                    <template #selected>
                      <div class="flex items-center gap-2">
                        <Avatar v-if="selectedInCurrencyOptionValue?.photo" class="size-6">
                          <AvatarImage :src="selectedInCurrencyOptionValue?.photo" alt="Agent Photo" />
                          <AvatarFallback>{{ selectedInCurrencyOptionValue?.name }}</AvatarFallback>
                        </Avatar>
                        <span>{{ dialog.in_currency_id ? selectedInCurrencyOptionValue?.value : `Select Currency In`
                          }}</span>
                      </div>
                    </template>
                    <CommandItem v-for="option in inCurrencyOptionValues" :key="option.value" :value="option.value"
                      @select="
                        (ev) => {
                          if (typeof ev.detail.value === 'string') {
                            dialog.in_currency_id = ev.detail.value
                          }
                          dialog.in_currency_id = option.id.toString()
                          inCurrencySelectIsOpen = false
                        }
                      ">
                      <div class="flex items-center gap-2">
                        <Avatar v-if="option.photo" class="size-6">
                          <AvatarImage :src="option.photo" alt="Currency Photo" />
                          <AvatarFallback>{{ option.name }}</AvatarFallback>
                        </Avatar>
                        <span>{{ option.value }}</span>
                      </div>
                      <Check class="ml-auto h-4 w-4"
                        :class="[dialog.in_currency_id === option.id.toString() ? 'opacity-100' : 'opacity-0']" />
                    </CommandItem>
                  </SelectInput>
                  <SelectInput v-if="isFieldVisible('out_currency_id')" v-model="outCurrencySelectIsOpen"
                    :error="errors.out_currency_id" label="Currency Out" :option-values="outCurrencyOptionValues"
                    :value="dialog.out_currency_id" popover-content-class="w-[359px] max-w-[calc(100vw-4rem)]">
                    <template #selected>
                      <div class="flex items-center gap-2">
                        <Avatar v-if="selectedOutCurrencyOptionValue?.photo" class="size-6">
                          <AvatarImage :src="selectedOutCurrencyOptionValue?.photo" alt="Currency Photo" />
                          <AvatarFallback>{{ selectedOutCurrencyOptionValue?.name }}</AvatarFallback>
                        </Avatar>
                        <span>{{ dialog.out_currency_id ? selectedOutCurrencyOptionValue?.value : `Select Currency Out`
                          }}</span>
                      </div>
                    </template>
                    <CommandItem v-for="option in outCurrencyOptionValues" :key="option.value" :value="option.value"
                      @select="
                        (ev) => {
                          if (typeof ev.detail.value === 'string') {
                            dialog.out_currency_id = ev.detail.value
                          }
                          dialog.out_currency_id = option.id.toString()
                          outCurrencySelectIsOpen = false
                        }
                      ">
                      <div class="flex items-center gap-2">
                        <Avatar v-if="option.photo" class="size-6">
                          <AvatarImage :src="option.photo" alt="Currency Photo" />
                          <AvatarFallback>{{ option.name }}</AvatarFallback>
                        </Avatar>
                        <span>{{ option.value }}</span>
                      </div>
                      <Check class="ml-auto h-4 w-4"
                        :class="[dialog.out_currency_id === option.id.toString() ? 'opacity-100' : 'opacity-0']" />
                    </CommandItem>
                  </SelectInput>
                  <FormattedNumberInput v-if="isFieldVisible('exchange_rate')" v-model="dialog.exchange_rate"
                    :error="errors.exchange_rate" label="Exchange Rate" />
                  <FormattedNumberInput v-if="isFieldVisible('receivable_amount')" v-model="dialog.receivable_amount"
                    :error="errors.receivable_amount" label="Receive Amount" />
                  <FormattedNumberInput v-if="isFieldVisible('payable_amount')" v-model="dialog.payable_amount"
                    :error="errors.payable_amount" label="Payable Amount" />
                  <FormattedNumberInput v-if="isFieldVisible('processing_fee')" v-model="dialog.processing_fee"
                    :error="errors.processing_fee" label="Processing Fee" />
                  <FormattedNumberInput
                    v-if="showCommissionField"
                    v-model="dialog.commission"
                    :error="errors.commission"
                    label="Commission"
                  />
                  <div v-if="dialog.currency_order_type_id === 'po'" class="flex items-center gap-4">
                    <div class="flex-grow">
                      <Label>Profit:</Label>
                      <div class="mt-1">RM{{ formatNumber(Number(dialog.profit)) }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <DialogFooter class="px-2">
                <div class="flex justify-between w-full">
                  <Button v-if="dialog.currency_order_type_id === 'po'" type="button" variant="destructive"
                    @click="calculateProfit" label="Calculate" />
                  <Button type="submit" label="Create Currency Order" />
                </div>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </div>
    <div class="bg-white overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead class="cursor-pointer" @click="changeSort('created_at')">
              <SortableHeader title="Created At" field="created_at" :current-sort="form.sort"
                :direction="form.direction" />
            </TableHead>
            <TableHead>Time Remaining</TableHead>
            <TableHead>Actions</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Reference</TableHead>
            <TableHead>In</TableHead>
            <TableHead>Out</TableHead>
            <TableHead>Receivable Amount <span class="font-normal">(Fulfilled Amount)</span></TableHead>
            <TableHead>Rate</TableHead>
            <TableHead>Payable Amount <span class="font-normal">(Fulfilled Amount)</span></TableHead>
            <TableHead>Processing Fee</TableHead>
            <TableHead>Profit And Loss</TableHead>
            <TableHead>Customer</TableHead>
            <TableHead>Created By</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="(currencyOrder, index) in currencyOrders.data" :key="currencyOrder.id" class="px-2"
            :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
            <TableCell>{{ currencyOrder.created_at }}</TableCell>
            <TableCell>
              <Countdown :timestamps="currencyOrder.status_timestamps" />
            </TableCell>
            <TableCell>
              <Link :href="route('currency-orders.edit', currencyOrder.id)">
              <Tooltip label="More Details">
                <Info />
              </Tooltip>
              </Link>
            </TableCell>
            <TableCell>
              <Badge class="text-xs rounded-full min-w-max" :class="{
                '!bg-[#ffc009] !text-primary': currencyOrder.status === 'Pending',
                '!bg-[#009A15]': currencyOrder.status === 'Completed' || currencyOrder.status === 'Closed',
                '!bg-[#dc3545]': currencyOrder.status === 'Partially Completed',
                '!bg-gray-500': currencyOrder.status === 'Cancelled',
              }">
                {{ currencyOrder.status }}
              </Badge>
            </TableCell>
            <TableCell>{{ currencyOrder.reference }}</TableCell>
            <TableCell>{{ currencyOrder.in_currency?.code }}</TableCell>
            <TableCell>{{ currencyOrder.out_currency?.code }}</TableCell>
            <TableCell>
              <div class="flex items-center space-x-2">
                <img v-if="currencyOrder.receivable_amount && currencyOrder.in_currency?.photo"
                  :src="currencyOrder.in_currency.photo" alt="In Currency" class="w-6 h-6 rounded-full" />
                <span>
                  {{ currencyOrder.in_currency?.code }} {{ currencyOrder.receivable_amount &&
                    formatNumber(Number(currencyOrder.receivable_amount)) }}
                  <span v-if="currencyOrder.receivable_amount"
                    :class="{ 'text-red-600': Number(currencyOrder.receivable_amount) !== Number(currencyOrder.fulfilled_receivable_amount) }">
                    ({{ currencyOrder.in_currency?.code }} {{ currencyOrder.fulfilled_receivable_amount }})
                  </span>
                </span>
              </div>
            </TableCell>
            <TableCell>{{ currencyOrder.exchange_rate && Number(currencyOrder.exchange_rate).toFixed(3) }}</TableCell>
            <TableCell>
              <div class="flex items-center space-x-2">
                <img v-if="currencyOrder.payable_amount && currencyOrder.out_currency?.photo"
                  :src="currencyOrder.out_currency.photo" alt="Out Currency" class="w-6 h-6 rounded-full" />
                <span>
                  {{ currencyOrder.out_currency?.code }} {{ currencyOrder.payable_amount &&
                    formatNumber(Number(currencyOrder.payable_amount)) }}
                  <span v-if="currencyOrder.payable_amount"
                    :class="{ 'text-red-600': Number(currencyOrder.payable_amount) !== Number(currencyOrder.fulfilled_payable_amount) }">
                    ({{ currencyOrder.out_currency?.code }} {{ currencyOrder.fulfilled_payable_amount }})
                  </span>
                </span>
              </div>
            </TableCell>
            <TableCell>
              <div v-if="currencyOrder.processing_fee" class="flex items-center space-x-2">
                <img v-if="currencyOrder.in_currency?.photo" :src="currencyOrder.in_currency.photo" alt="Out Currency"
                  class="w-6 h-6 rounded-full" />
                <span>{{ currencyOrder.in_currency?.code }} {{ formatNumber(Number(currencyOrder.processing_fee)) }}</span>
              </div>
            </TableCell>
            <TableCell>RM{{ currencyOrder.profit_loss ? formatNumber(Number(currencyOrder.profit_loss).toFixed(2)) : '0.00' }}
            </TableCell>
            <TableCell>{{ currencyOrder.customer }}</TableCell>
            <TableCell>{{ currencyOrder.created_by }}</TableCell>
          </TableRow>
          <TableRow v-if="currencyOrders.data.length === 0">
            <TableCell class="text-center border-0" colspan="14">No currency orders found.</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
    <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
      <ShowEntries v-model="form.perPage" />
      <Pagination class="flex justify-end" :data="currencyOrders" />
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3'
import throttle from 'lodash/throttle'
import Layout from '@/Shared/Layout.vue'
import pickBy from 'lodash/pickBy'
import axios from 'axios'
import { Card, CardContent } from '@/Components/ui/card'
import SearchFilter from '@/Shared/SearchFilter.vue'
import { Label } from '@/Components/ui/label'
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select'
import { Separator } from '@/Components/ui/separator'
import ShowEntries from '@/Shared/ShowEntries.vue'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/Components/ui/dialog'
import Button from '@/Shared/Button.vue'
import SelectInput from '@/Shared/SelectInput.vue'
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar'
import TextInput from '@/Shared/TextInput.vue'
import FormattedNumberInput from '@/Shared/FormattedNumberInput.vue'
import { TableRow } from '@/Components/ui/table'
import { TableCell, TableHead, TableHeader, TableBody, Table } from '@/Shared/table'
import SortableHeader from '@/Shared/SortableHeader.vue'
import Pagination from '@/Shared/Pagination.vue'
import { Badge } from '@/Components/ui/badge'
import Countdown from '@/Shared/Countdown.vue'
import Tooltip from '@/Shared/Tooltip.vue'
import { Check, Info } from 'lucide-vue-next'
import Breadcrumb from '@/Shared/Breadcrumb.vue'
import { CommandItem } from '@/Components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/Components/ui/popover'
import { Calendar as CalendarIcon } from 'lucide-vue-next'
import { RangeCalendar } from '@/Components/ui/range-calendar'
import { Button as UIButton } from '@/Components/ui/button'
import { CalendarDate, DateFormatter, getLocalTimeZone } from '@internationalized/date'
import { Input } from '@/Components/ui/input'

export default {
  components: {
    Head,
    Link,
    Card,
    CardContent,
    SearchFilter,
    Label,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
    Separator,
    ShowEntries,
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    Button,
    SelectInput,
    Avatar,
    AvatarFallback,
    AvatarImage,
    TextInput,
    FormattedNumberInput,
    Table,
    TableBody,
    TableHeader,
    TableRow,
    TableCell,
    TableHead,
    SortableHeader,
    Pagination,
    Badge,
    Countdown,
    Tooltip,
    Info,
    Breadcrumb,
    CommandItem,
    Check,
    Popover,
    PopoverContent,
    PopoverTrigger,
    CalendarIcon,
    RangeCalendar,
    UIButton,
    Input,
  },
  layout: Layout,
  props: {
    filters: Object,
    currencyOrders: Object,
    sort: Object,
    currencyOrderStatuses: Array,
  },
  data() {
    const start_date = this.filters.start_date ? parseDate(this.filters.start_date) : null;
    const end_date = this.filters.end_date ? parseDate(this.filters.end_date) : null;

    return {
      currentPage: null, // Track the current page
      defaultValues: {
        sort: 'created_at',
        direction: 'desc',
        perPage: 10,
        currency_in: '',
        currency_out: '',
        customer: '',
        created_by: '',
        currency_order_type: '',
        status: null,
        start_date: null,
        end_date: null,
      },
      form: {
        reference: this.filters.reference || '',
        sort: this.filters.sort || 'created_at',
        direction: this.filters.direction || 'desc',
        perPage: this.filters.perPage?.toString() || '10',
        start_date: this.filters.start_date || null,
        end_date: this.filters.end_date || null,
        currency_in: this.filters.currency_in || '',
        currency_out: this.filters.currency_out || '',
        customer: this.filters.customer || '',
        created_by: this.filters.created_by || '',
        currency_order_type: this.filters.currency_order_type || '',
        status: this.filters.status || null,
      },
      dialog: {
        currency_order_type_id: '',
        customer_id: '',
        in_currency_id: '',
        out_currency_id: '',
        exchange_rate: '',
        initial_rate: '',
        receivable_amount: '',
        payable_amount: '',
        processing_fee: '',
        commission: '',
        profit: '0.00',
      },
      customers: [],
      currencies: [],
      currencyOrderTypes: [],
      specialCustomers: {
        expense: null,
        revenue: null,
      },
      errors: {},
      createDialogIsOpen: false,
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Currency Orders', link: route('currency-orders'), is_active: true },
      ],
      customerSelectIsOpen: false,
      inCurrencySelectIsOpen: false,
      outCurrencySelectIsOpen: false,
      rateOperation: null,
      ignoreWatch: false,
      lastModifiedField: null,
      df: new DateFormatter('en-US', {
        dateStyle: 'medium',
      }),
      value: {
        start: start_date,
        end: end_date
      },
    }
  },
  watch: {
    filters: {
      handler(newFilters) {

        this.form = {
          reference: newFilters.reference || '',
          sort: newFilters.sort || 'created_at',
          direction: newFilters.direction || 'desc',
          perPage: newFilters.perPage?.toString() || '10',
          start_date: newFilters.start_date || null,
          end_date: newFilters.end_date || null,
          currency_in: newFilters.currency_in || '',
          currency_out: newFilters.currency_out || '',
          customer: newFilters.customer || '',
          created_by: newFilters.created_by || '',
          currency_order_type: newFilters.currency_order_type || '',
          status: newFilters.status || null,
        };

        if (newFilters.start_date || newFilters.end_date) {
          this.value = {
            start: newFilters.start_date ? parseDate(newFilters.start_date) : null,
            end: newFilters.end_date ? parseDate(newFilters.end_date) : null,
          };
        }
      },
      immediate: true,
      deep: true,
    },
    'form.perPage': function () {
      // When changing perPage, we want to reset to page 1
      // This is handled in performSearch method
      const params = { ...this.form };
      this.performSearch(params);
    },
    'dialog.in_currency_id': function (newVal) {
      if (this.dialog.currency_order_type_id === 'po') {
        if (newVal === this.dialog.out_currency_id) {
          this.dialog.out_currency_id = ''
        }
        this.fetchExchangeRate()
      }
    },
    'dialog.out_currency_id': function (newVal) {
      if (this.dialog.currency_order_type_id === 'po') {
        if (newVal === this.dialog.in_currency_id) {
          this.dialog.in_currency_id = ''
        }
        this.fetchExchangeRate()
      }
    },
    'dialog.receivable_amount': function (newVal) {
      if (!this.dialog.exchange_rate || this.ignoreWatch) return

      this.lastModifiedField = 'receivable'
      this.ignoreWatch = true
      if (!newVal || newVal === '') {
        this.dialog.payable_amount = ''
      } else {
        const numVal = parseFloat(newVal)
        if (!isNaN(numVal)) {
          const rate = parseFloat(this.dialog.exchange_rate)
          const calculatedValue = this.rateOperation === 'multiply' ? (numVal * rate).toFixed(2) : (numVal / rate).toFixed(2)
          this.dialog.payable_amount = calculatedValue
        }
      }
      this.$nextTick(() => {
        this.ignoreWatch = false
      })
    },
    'dialog.payable_amount': function (newVal) {
      if (!this.dialog.exchange_rate || this.ignoreWatch) return

      this.lastModifiedField = 'payable'
      this.ignoreWatch = true
      if (!newVal || newVal === '') {
        this.dialog.receivable_amount = ''
      } else {
        const numVal = parseFloat(newVal)
        if (!isNaN(numVal)) {
          const rate = parseFloat(this.dialog.exchange_rate)
          const calculatedValue = this.rateOperation === 'multiply' ? (numVal / rate).toFixed(2) : (numVal * rate).toFixed(2)
          this.dialog.receivable_amount = calculatedValue
        }
      }
      this.$nextTick(() => {
        this.ignoreWatch = false
      })
    },
    'dialog.exchange_rate': function (newVal) {
      if (!newVal || this.ignoreWatch) return

      this.ignoreWatch = true
      if (newVal === '') {
        this.dialog.payable_amount = ''
        this.dialog.receivable_amount = ''
      } else {
        const rate = parseFloat(newVal)
        if (!isNaN(rate)) {
          if (this.lastModifiedField === 'receivable') {
            const receivable = parseFloat(this.dialog.receivable_amount)
            if (!isNaN(receivable)) {
              const calculatedValue = this.rateOperation === 'multiply' ? (receivable * rate).toFixed(2) : (receivable / rate).toFixed(2)
              this.dialog.payable_amount = calculatedValue
            }
          } else if (this.lastModifiedField === 'payable') {
            const payable = parseFloat(this.dialog.payable_amount)
            if (!isNaN(payable)) {
              const calculatedValue = this.rateOperation === 'multiply' ? (payable / rate).toFixed(2) : (payable * rate).toFixed(2)
              this.dialog.receivable_amount = calculatedValue
            }
          }
        }
      }
      this.$nextTick(() => {
        this.ignoreWatch = false
      })
    },
    '$page.url': {
      handler() {
        const urlParams = new URLSearchParams(window.location.search);

        // Update form with URL parameters
        this.form = {
          reference: urlParams.get('reference') || '',
          sort: urlParams.get('sort') || 'created_at',
          direction: urlParams.get('direction') || 'desc',
          perPage: urlParams.get('perPage')?.toString() || '10',
          start_date: urlParams.get('start_date') || null,
          end_date: urlParams.get('end_date') || null,
          currency_in: urlParams.get('currency_in') || '',
          currency_out: urlParams.get('currency_out') || '',
          customer: urlParams.get('customer') || '',
          created_by: urlParams.get('created_by') || '',
          currency_order_type: urlParams.get('currency_order_type') || '',
          status: urlParams.get('status') || null,
        };

        // Store the current page for reference
        if (urlParams.get('page')) {
          this.currentPage = urlParams.get('page');
        }

        if (urlParams.get('start_date') || urlParams.get('end_date')) {
          this.value = {
            start: urlParams.get('start_date') ? parseDate(urlParams.get('start_date')) : null,
            end: urlParams.get('end_date') ? parseDate(urlParams.get('end_date')) : null,
          };
        }
      },
      immediate: true,
      deep: true
    },
  },
  created() {
    // Initialize from URL params on creation
    const urlParams = new URLSearchParams(window.location.search);

    // Initialize currentPage from URL
    if (urlParams.get('page')) {
      this.currentPage = urlParams.get('page');
    }

    Object.keys(this.form).forEach(key => {
      const urlValue = urlParams.get(key);
      if (urlValue !== null) {
        this.form[key] = urlValue;
      }
    });
  },
  mounted() {
    this.loadData();

    const urlParams = new URLSearchParams(window.location.search);

    // Initialize currentPage from URL
    if (urlParams.get('page')) {
      this.currentPage = urlParams.get('page');
    }

    this.form = {
      reference: urlParams.get('reference') || this.filters.reference || '',
      sort: urlParams.get('sort') || this.filters.sort || 'created_at',
      direction: urlParams.get('direction') || this.filters.direction || 'desc',
      perPage: (urlParams.get('perPage') || this.filters.perPage)?.toString() || '10',
      start_date: urlParams.get('start_date') || this.filters.start_date || null,
      end_date: urlParams.get('end_date') || this.filters.end_date || null,
      currency_in: urlParams.get('currency_in') || this.filters.currency_in || '',
      currency_out: urlParams.get('currency_out') || this.filters.currency_out || '',
      customer: urlParams.get('customer') || this.filters.customer || '',
      created_by: urlParams.get('created_by') || this.filters.created_by || '',
      currency_order_type: urlParams.get('currency_order_type') || this.filters.currency_order_type || '',
      status: urlParams.get('status') || this.filters.status || null,
    };

    // Initialize date range if needed
    if (urlParams.get('start_date') || urlParams.get('end_date')) {
      this.value = {
        start: urlParams.get('start_date') ? parseDate(urlParams.get('start_date')) : null,
        end: urlParams.get('end_date') ? parseDate(urlParams.get('end_date')) : null,
      };
    }
  },
  computed: {
    filteredOutCurrencies() {
      if (this.dialog.currency_order_type_id === 'po') {
        if (!this.dialog.in_currency_id) {
          return this.currencies;
        }
        return this.currencies.filter((currency) => currency.id.toString() !== this.dialog.in_currency_id);
      }
      return this.currencies;
    },
    filteredInCurrencies() {
      if (this.dialog.currency_order_type_id === 'po') {
        if (!this.dialog.out_currency_id) {
          return this.currencies;
        }
        return this.currencies.filter((currency) => currency.id.toString() !== this.dialog.out_currency_id);
      }
      return this.currencies;
    },
    customerOptionValues() {
      return this.filteredCustomers().map((customer) => ({ ...customer, value: customer.name }))
    },
    selectedCustomerOptionValue() {
      return this.customerOptionValues.find((option) => option.id.toString() === this.dialog.customer_id)
    },
    inCurrencyOptionValues() {
      return this.currencies.map((currency) => ({ ...currency, value: `${currency.name} (${currency.code})` }))
    },
    selectedInCurrencyOptionValue() {
      return this.inCurrencyOptionValues.find((option) => option.id.toString() === this.dialog.in_currency_id)
    },
    outCurrencyOptionValues() {
      return this.filteredOutCurrencies.map((currency) => ({ ...currency, value: `${currency.name} (${currency.code})` }))
    },
    selectedOutCurrencyOptionValue() {
      return this.outCurrencyOptionValues.find((option) => option.id.toString() === this.dialog.out_currency_id)
    },
    formattedDateResult() {
      if (!this.value.start) {
        return 'Select Date Range'
      }

      if (!this.value.end) {
        return this.df.format(this.value.start.toDate(getLocalTimeZone()))
      }

      return `${this.df.format(this.value.start.toDate(getLocalTimeZone()))} - ${this.df.format(this.value.end.toDate(getLocalTimeZone()))}`
    },
    computedCurrencyOrderTypes() {
      return this.currencyOrderTypes.map(type => ({
        value: type.value,
        label: type.name
      }))
    },
    statusOptions() {
      return this.currencyOrderStatuses.map(status => ({
        value: status.value,
        label: status.name
      }))
    },
    canCreateCurrencyOrder() {
      return this.$page.props.auth.user.roles.some(role =>
        role.permissions.some(permission =>
          permission.name === 'create currency order'
        )
      );
    },
    canExportCurrencyOrder() {
      return this.$page.props.auth.user.roles.some(role =>
        role.permissions.some(permission =>
          permission.name === 'export currency order'
        )
      );
    },
    selectedCustomer() {
      if (!this.dialog.customer_id) return null;
      return this.customers.find(customer => customer.id.toString() === this.dialog.customer_id);
    },
    showCommissionField() {
      return this.dialog.currency_order_type_id === 'po' &&
             this.selectedCustomer &&
             this.selectedCustomer.agent_id;
    },
  },
  methods: {
    formatNumber(value) {
      if (value === null || value === undefined) return '0.00';
      return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 3
      }).format(value);
    },
    async loadData() {
      try {
        const [customersResponse, currenciesResponse, orderTypesResponse] = await Promise.all([axios.get('/customers/all'), axios.get('/currencies/all'), axios.get('/currency-order-types/all')])

        this.customers = customersResponse.data
        this.currencies = currenciesResponse.data
        this.currencyOrderTypes = orderTypesResponse.data

        this.specialCustomers.expense = this.customers.find((c) => c.name === 'Expense')?.id
        this.specialCustomers.revenue = this.customers.find((c) => c.name === 'Revenue')?.id
      } catch (error) {
        console.error('Failed to load data:', error)
      }
    },
    handleTypeChange(newType) {
      this.dialog.currency_order_type_id = newType;

      // Reset currency fields and related data
      this.dialog.in_currency_id = '';
      this.dialog.out_currency_id = '';
      this.dialog.exchange_rate = '';
      this.dialog.initial_rate = '';
      this.dialog.receivable_amount = '';
      this.dialog.payable_amount = '';
      this.dialog.processing_fee = '';
      this.dialog.commission = '';
      this.dialog.profit = '0.00';

      // Set special customer IDs for 'e' and 'r' types
      if (newType === 'e') {
        this.dialog.customer_id = this.specialCustomers.expense.toString();
      } else if (newType === 'r') {
        this.dialog.customer_id = this.specialCustomers.revenue.toString();
      } else {
        this.dialog.customer_id = '';
      }
    },
    filteredCustomers() {
      if (this.dialog.currency_order_type_id === 'e' || this.dialog.currency_order_type_id === 'r') {
        return this.customers
      } else {
        return this.customers.filter((customer) => customer.id !== this.specialCustomers.expense && customer.id !== this.specialCustomers.revenue)
      }
    },
    reset() {
      this.form = {
        reference: null,
        sort: this.defaultValues.sort,
        direction: this.defaultValues.direction,
        perPage: this.defaultValues.perPage.toString(),
        start_date: null,
        end_date: null,
        currency_in: '',
        currency_out: '',
        customer: '',
        created_by: '',
        currency_order_type: '',
        status: null,
      }
      this.value = {
        start: null,
        end: null
      }

      this.dialog = {
        currency_order_type_id: '',
        customer_id: '',
        in_currency_id: '',
        out_currency_id: '',
        exchange_rate: '',
        initial_rate: '',
        receivable_amount: '',
        payable_amount: '',
        processing_fee: '',
        commission: '',
      }
      this.errors = {}

      this.$inertia.visit('/currency-orders', {
        preserveScroll: true,
        replace: true
      });
    },
    search() {
      this.performSearch(this.form);
    },
    performSearch(params) {
      // Make sure we're using only valid parameters
      const filteredParams = pickBy(params, value => value !== null);

      // Get current URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      const currentPage = urlParams.get('page');

      // If we're changing perPage, we need to reset to page 1
      // This is the expected behavior when changing items per page
      if (params.perPage && params.perPage !== urlParams.get('perPage')) {
        // Reset to page 1 when changing perPage
        delete filteredParams.page;
      } else if (currentPage && !params.page) {
        // Otherwise preserve the current page if it exists
        filteredParams.page = currentPage;
      }

      this.$inertia.get('/currency-orders', filteredParams, {
        preserveState: true,
        preserveScroll: true,
        only: ['currencyOrders'],
        replace: true
      });
    },
    changeSort(field) {
      this.form.direction =
        this.form.sort === field
          ? this.form.direction === "asc"
            ? "desc"
            : "asc"
          : "asc";
      this.form.sort = field;

      this.performSearch(this.form);
    },
    isFieldVisible(field) {
      const visibleFields = {
        e: ['customer', 'out_currency_id', 'payable_amount'],
        po: ['customer', 'in_currency_id', 'out_currency_id', 'exchange_rate', 'receivable_amount', 'payable_amount', 'processing_fee'],
        r: ['customer', 'in_currency_id', 'exchange_rate', 'receivable_amount'],
        tpp: ['customer', 'out_currency_id', 'payable_amount'],
        tpr: ['customer', 'in_currency_id', 'exchange_rate', 'receivable_amount', 'processing_fee'],
        commission: ['customer', 'in_currency_id', 'exchange_rate', 'receivable_amount'],
      }

      const currentType = this.dialog.currency_order_type_id || ''
      return visibleFields[currentType]?.includes(field)
    },
    createCurrencyOrder() {
      this.dialog.payable_amount = ''
      this.dialog.receivable_amount = ''
      this.dialog.commission = ''
      this.dialog.processing_fee = ''
      this.dialog.exchange_rate = ''
      this.dialog.customer_id = ''
      this.dialog.currency_order_type_id = ''
      this.dialog.in_currency_id = ''
      this.dialog.out_currency_id = ''
      this.createDialogIsOpen = true
    },
    submitCreateCurrencyOrder() {
      // Helper function to remove commas from formatted numbers
      const cleanNumber = (value) => {
        return value ? value.toString().replace(/,/g, '') : value;
      };

      const formData = {
        customer_id: this.dialog.customer_id,
        currency_order_type_id: this.dialog.currency_order_type_id,
      };

      if (this.dialog.currency_order_type_id === 'e') {
        formData.out_currency_id = this.dialog.out_currency_id;
        formData.payable_amount = cleanNumber(this.dialog.payable_amount);
      } else if (this.dialog.currency_order_type_id === 'po') {
        formData.in_currency_id = this.dialog.in_currency_id;
        formData.out_currency_id = this.dialog.out_currency_id;
        formData.exchange_rate = cleanNumber(this.dialog.exchange_rate);
        formData.initial_rate = cleanNumber(this.dialog.initial_exchange_rate);
        formData.receivable_amount = cleanNumber(this.dialog.receivable_amount);
        formData.payable_amount = cleanNumber(this.dialog.payable_amount);
        formData.processing_fee = cleanNumber(this.dialog.processing_fee);
        formData.commission = cleanNumber(this.dialog.commission);
      } else if (this.dialog.currency_order_type_id === 'r') {
        formData.in_currency_id = this.dialog.in_currency_id;
        formData.exchange_rate = cleanNumber(this.dialog.exchange_rate);
        formData.receivable_amount = cleanNumber(this.dialog.receivable_amount);
      } else if (this.dialog.currency_order_type_id === 'tpp') {
        formData.out_currency_id = this.dialog.out_currency_id;
        formData.payable_amount = cleanNumber(this.dialog.payable_amount);
      } else if (this.dialog.currency_order_type_id === 'tpr') {
        formData.in_currency_id = this.dialog.in_currency_id;
        formData.exchange_rate = cleanNumber(this.dialog.exchange_rate);
        formData.receivable_amount = cleanNumber(this.dialog.receivable_amount);
        formData.processing_fee = cleanNumber(this.dialog.processing_fee);
      }

      this.$inertia.post(
        route('currency-orders.store'),
        formData,
        {
          preserveScroll: true,
          onSuccess: () => {
            this.dialog.payable_amount = '';
            this.dialog.receivable_amount = '';
            this.dialog.commission = '';
            this.dialog.processing_fee = '';
            this.dialog.exchange_rate = '';
            this.dialog.initial_exchange_rate = '';
            this.dialog.customer_id = '';
            this.dialog.currency_order_type_id = '';
            this.dialog.in_currency_id = '';
            this.dialog.out_currency_id = '';
            this.errors = {};
            this.createDialogIsOpen = false;
          },
          onError: (errors) => {
            this.errors = errors;
          },
        },
      );
    },
    async fetchExchangeRate() {
      if (this.dialog.in_currency_id && this.dialog.out_currency_id && this.dialog.in_currency_id !== this.dialog.out_currency_id) {
        try {
          const response = await axios.get(route('exchange-rates.find-rate'), {
            params: {
              currency_in_id: this.dialog.in_currency_id,
              currency_out_id: this.dialog.out_currency_id,
            },
          })

          if (response.data.success && response.data.rate) {
            this.dialog.exchange_rate = Number(response.data.rate).toFixed(10)
            this.dialog.initial_exchange_rate = this.dialog.exchange_rate
            this.rateOperation = response.data.operation

            this.dialog.receivable_amount = ''
            this.dialog.payable_amount = ''
            this.dialog.profit = '0.00'
          }
        } catch (error) {
          console.error('Failed to fetch exchange rate:', error)
        }
      } else {
        this.dialog.exchange_rate = ''
        this.dialog.initial_exchange_rate = ''
        this.rateOperation = null
        this.dialog.profit = '0.00'
      }
    },
    async calculateProfit() {
      if (!this.dialog.initial_exchange_rate || !this.dialog.exchange_rate || !this.dialog.payable_amount || !this.dialog.receivable_amount || !this.dialog.in_currency_id || !this.dialog.out_currency_id) {
        this.dialog.profit = '0.00'
        return
      }

      try {
        // Ensure we're using raw numeric values without commas
        const receivableAmount = parseFloat(this.dialog.receivable_amount.toString().replace(/,/g, ''))
        const payableAmount = parseFloat(this.dialog.payable_amount.toString().replace(/,/g, ''))
        const processingFee = this.dialog.processing_fee ? parseFloat(this.dialog.processing_fee.toString().replace(/,/g, '')) : 0
        const commission = this.dialog.commission ? parseFloat(this.dialog.commission.toString().replace(/,/g, '')) : 0
        const exchangeRate = parseFloat(this.dialog.exchange_rate.toString().replace(/,/g, ''))
        const initialRate = parseFloat(this.dialog.initial_exchange_rate.toString().replace(/,/g, ''))

        const response = await axios.post(route('exchange-rates.calculate-profit'), {
          initial_rate: initialRate,
          manual_rate: exchangeRate,
          receivable_amount: receivableAmount,
          payable_amount: payableAmount,
          currency_in: this.selectedInCurrencyOptionValue?.code,
          currency_out: this.selectedOutCurrencyOptionValue?.code,
          processing_fee: processingFee,
          commission: commission,
        })

        if (response.data.success) {
          this.dialog.profit = response.data.profit
        }
      } catch (error) {
        console.error('Failed to calculate profit:', error)
        this.dialog.profit = '0.00'
      }
    },
    handleDateChange(newValue) {
      this.value = newValue;
      if (newValue.start) {
        this.form.start_date = newValue.start.toString();
      } else {
        this.form.start_date = null;
      }

      if (newValue.end) {
        this.form.end_date = newValue.end.toString();
      } else {
        this.form.end_date = null;
      }
    },
    syncFormWithUrl() {
      const urlParams = new URLSearchParams(window.location.search);

      Object.keys(this.form).forEach(key => {
        const urlValue = urlParams.get(key);
        if (urlValue !== null) {
          this.form[key] = urlValue;
        }
      });

      if (urlParams.get('start_date') || urlParams.get('end_date')) {
        this.value = {
          start: urlParams.get('start_date') ? parseDate(urlParams.get('start_date')) : null,
          end: urlParams.get('end_date') ? parseDate(urlParams.get('end_date')) : null,
        };
      }
    },
  },
}

function parseDate(dateString) {
  const [year, month, day] = dateString.split('-').map(Number);
  return new CalendarDate(year, month, day);
}
</script>
