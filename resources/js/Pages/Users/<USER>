<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head title="Users" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <Card>
      <CardContent class="p-0">
        <div class="p-4">
          <SearchFilter @reset="reset">
            <div>
              <Label class="mb-2 inline-block">Name:</Label>
              <Input v-model="form.name" type="text" name="name" placeholder="Enter name" autocomplete="off" />
            </div>
            <div>
              <Label class="mb-2 inline-block">Email:</Label>
              <Input v-model="form.email" type="text" name="email" placeholder="Enter email" autocomplete="off" />
            </div>
            <div>
              <Label class="mb-2 inline-block">Role:</Label>
              <Select v-model="form.role" class="form-select mt-1 w-full">
                <SelectTrigger>
                  <SelectValue placeholder="Select Role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem v-for="role in roleOptionValues" :key="role.value" :value="role.value">
                      {{ role.value.charAt(0).toUpperCase() + role.value.slice(1) }}
                    </SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label class="mb-2 inline-block">Status:</Label>
              <Select v-model="form.status" class="form-select mt-1 w-full">
                <SelectTrigger>
                  <SelectValue placeholder="Select Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem v-for="status in statusOptions" :key="status.value" :value="status.value">
                      {{ status.label }}
                    </SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <template #actions>
              <Button type="button" label="Search" @click="search" class="cursor-pointer" />
            </template>
          </SearchFilter>
        </div>
      </CardContent>
    </Card>
    <div>
      <Separator class="my-5" />
    </div>
    <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
      <h1 class="text-2xl font-bold">Users</h1>
      <div>
        <Dialog v-model:open="createDialogIsOpen">
          <Button v-if="canCreateUser" label="Create" icon="plus" class="cursor-pointer" @click="createUser" />
          <DialogContent class="sm:max-w-[425px]">
            <DialogHeader class="px-2">
              <DialogTitle>Create User</DialogTitle>
              <DialogDescription></DialogDescription>
            </DialogHeader>
            <form @submit.prevent="submitCreateUser">
              <div class="overflow-y-auto mb-4 p-2">
                <div class="grid gap-4 max-h-[75vh]">
                  <TextInput v-model="dialog.name" :error="errors.name" label="Name" />
                  <TextInput v-model="dialog.email" :error="errors.email" label="Email Address" />
                  <TextInput v-model="dialog.password" :error="errors.password" label="Password" />
                  <SelectInput v-model="roleSelectIsOpen" :error="errors.role" label="Role"
                    :option-values="roleOptionValues" :value="dialog.role"
                    popover-content-class="w-[359px] max-w-[calc(100vw-4rem)]">
                    <template #selected>
                      <span class="capitalize">
                        {{dialog.role
                          ? roleOptionValues.find(option => option.value === dialog.role)?.value
                          : `Select Role`}}
                      </span>
                    </template>
                    <CommandItem v-for="option in roleOptionValues" :key="option.value" :value="option.value" @select="(ev) => {
                      if (typeof ev.detail.value === 'string') {
                        dialog.role = ev.detail.value
                      }
                      dialog.role = option.value
                      roleSelectIsOpen = false
                    }">
                      <span class="capitalize">{{ option.value }}</span>
                      <Check class="ml-auto h-4 w-4" :class="[
                        dialog.role === option.value ? 'opacity-100' : 'opacity-0'
                      ]" />
                    </CommandItem>
                  </SelectInput>
                  <SwitchInput v-model="dialog.is_active" :error="errors.is_active" label="Status">
                    <Label>{{ dialog.is_active === true ? 'Active' : 'Inactive' }}</Label>
                  </SwitchInput>
                </div>
              </div>
              <DialogFooter class="px-2">
                <Button type="submit" label="Create User" />
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </div>
    <div class="bg-white overflow-x-auto mb-6">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Avatar</TableHead>
            <TableHead class="cursor-pointer" @click="changeSort('name')">
              <SortableHeader title="Name" field="name" :current-sort="form.sort" :direction="form.direction" />
            </TableHead>
            <TableHead class="cursor-pointer" @click="changeSort('email')">
              <SortableHeader title="Email" field="email" :current-sort="form.sort" :direction="form.direction" />
            </TableHead>
            <TableHead>Role</TableHead>
            <TableHead>Status</TableHead>
            <TableHead class="cursor-pointer" @click="changeSort('created_at')">
              <SortableHeader title="Created At" field="created_at" :current-sort="form.sort"
                :direction="form.direction" />
            </TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="(user, index) in users.data" :key="user.id" :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
            <TableCell class="text-center">
              <Avatar class="size-6 align-middle">
                <AvatarImage :src="user.photo" alt="User avatar" />
                <AvatarFallback>{{ user.name }}</AvatarFallback>
              </Avatar>
            </TableCell>
            <TableCell>
              <Link class="flex items-center" :href="route('users.edit', user.id)">
              {{ user.name }}
              <Icon v-if="user.deleted_at" name="trash" class="shrink-0 ml-2 w-3 h-3 fill-gray-400" />
              </Link>
            </TableCell>
            <TableCell>
              <Link class="flex items-center" :href="route('users.edit', user.id)" tabindex="-1">
              {{ user.email }}
              </Link>
            </TableCell>
            <TableCell>
              <div class="flex gap-1">
                <Badge v-for="role in user.roles" :key="role">
                  {{ role.charAt(0).toUpperCase() + role.slice(1) }}
                </Badge>
              </div>
            </TableCell>
            <TableCell>
              <span class="px-3 py-1 text-xs font-medium rounded-full"
                :class="user.is_active === true ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                {{ user.is_active === true ? 'Active' : 'Inactive' }}
              </span>
            </TableCell>
            <TableCell>
              {{ user.created_at }}
            </TableCell>
            <TableCell>
              <Link :href="route('users.edit', user.id)">
              <Tooltip label="More Details">
                <Info />
              </Tooltip>
              </Link>
            </TableCell>
          </TableRow>
          <TableRow v-if="users.data.length === 0">
            <TableCell class="text-center border-0" colspan="7">No users found.</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
    <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
      <ShowEntries v-model="form.perPage" />
      <Pagination class="flex justify-end" :data="users" />
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import pickBy from 'lodash/pickBy';
import Layout from '@/Shared/Layout.vue';
import throttle from 'lodash/throttle';
import axios from 'axios';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import SearchFilter from '@/Shared/SearchFilter.vue';
import { Label } from '@/Components/ui/label';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/Components/ui/select';
import { Separator } from '@/Components/ui/separator';
import ShowEntries from '@/Shared/ShowEntries.vue';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/Components/ui/dialog';
import Button from '@/Shared/Button.vue';
import TextInput from '@/Shared/TextInput.vue';
import SelectInput from '@/Shared/SelectInput.vue';
import SwitchInput from '@/Shared/SwitchInput.vue';
import {
  TableRow
} from '@/Components/ui/table';
import SortableHeader from '@/Shared/SortableHeader.vue';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';
import Icon from '@/Shared/Icon.vue';
import { Badge } from '@/Components/ui/badge';
import Pagination from '@/Shared/Pagination.vue';
import { TableHead, TableCell, TableHeader, TableBody, Table } from '@/Shared/table';
import { Check, Info } from 'lucide-vue-next';
import Tooltip from '@/Shared/Tooltip.vue';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { CommandItem } from '@/Components/ui/command';
import { Input } from '@/Components/ui/input';

export default {
  components: {
    Head,
    Link,
    Card,
    CardContent,
    SearchFilter,
    Label,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
    Separator,
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    Button,
    TextInput,
    SelectInput,
    SwitchInput,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    SortableHeader,
    Avatar,
    AvatarFallback,
    AvatarImage,
    Icon,
    Badge,
    Pagination,
    ShowEntries,
    Info,
    Tooltip,
    Breadcrumb,
    CommandItem,
    Check,
    Input
  },
  layout: Layout,
  props: {
    filters: Object,
    users: Object,
    sort: Object,
  },
  data() {
    return {
      defaultValues: {
        sort: 'created_at',
        direction: 'desc',
        perPage: 10,
        role: null,
        status: null,
      },
      form: {
        name: this.filters.name,
        email: this.filters.email,
        role: this.filters.role,
        status: this.filters.status,
        sort: this.sort?.field ?? 'created_at',
        direction: this.sort?.direction ?? 'desc',
        perPage: this.filters.perPage?.toString() || '10',
      },
      dialog: {
        id: null,
        name: '',
        email: '',
        password: '',
        role: '',
        is_active: true,
      },
      errors: {},
      createDialogIsOpen: false,
      roles: [],
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Users', link: route('users'), is_active: true },
      ],
      roleSelectIsOpen: false,
      canCreateUser: false,
      canViewUser: false,
    };
  },
  created() {
    this.checkPermissions();
  },
  mounted() {
    this.loadRoles();
  },
  watch: {
    'form.perPage': function(newValue) {
      this.search();
    }
  },
  methods: {
    async loadRoles() {
      try {
        const response = await axios.get('/roles/all');
        this.roles = response.data;
      } catch (error) {
        console.error('Failed to load roles:', error);
      }
    },
    capitalizeFirstLetter(string) {
      return string.charAt(0).toUpperCase() + string.slice(1);
    },
    reset() {
      this.form = {
        name: null,
        email: null,
        role: null,
        status: null,
        sort: this.defaultValues.sort,
        direction: this.defaultValues.direction,
        perPage: this.defaultValues.perPage.toString(),
      };

      this.$inertia.visit('/users', {
        preserveScroll: true,
        replace: true
      });
    },
    search() {
      this.performSearch(this.form);
    },
    performSearch(params) {
      this.$inertia.get('/users', pickBy(params, value => value !== null), {
        preserveState: true,
        preserveScroll: true,
        only: ['users'],
        replace: true
      });
    },
    changeSort(field) {
      this.form.direction = this.form.sort === field
        ? this.form.direction === 'asc'
          ? 'desc'
          : 'asc'
        : 'asc';
      this.form.sort = field;
      this.search();
    },
    createUser() {
      this.dialog.id = null;
      this.dialog.name = '';
      this.dialog.email = '';
      this.dialog.password = '';
      this.dialog.role = '';
      this.dialog.is_active = true;
      this.createDialogIsOpen = true;
    },
    submitCreateUser() {
      this.$inertia.post(route('users.store'), {
        name: this.dialog.name,
        email: this.dialog.email,
        password: this.dialog.password,
        role: this.dialog.role,
        is_active: this.dialog.is_active,
      }, {
        preserveScroll: true,
        onSuccess: () => {
          this.dialog.id = null;
          this.dialog.name = '';
          this.dialog.email = '';
          this.dialog.password = '';
          this.dialog.role = '';
          this.dialog.is_active = true;
          this.errors = {};
          this.createDialogIsOpen = false;
        },
        onError: (errors) => {
          this.errors = errors;
        },
      });
    },
    checkPermissions() {
      const userRoles = this.$page.props.auth.user.roles || [];
      
      // Check if user has permission to create users
      this.canCreateUser = userRoles.some(role => 
        role.permissions && role.permissions.some(permission => 
          permission.name === 'create setting user'
        )
      );
      
      // Check if user has permission to view users
      this.canViewUser = userRoles.some(role => 
        role.permissions && role.permissions.some(permission => 
          permission.name === 'view setting user'
        )
      );
    },
  },
  computed: {
    roleOptionValues() {
      return this.roles.map(role => ({ ...role, value: role.name }));
    },
    statusOptions() {
      return [
        { value: 'true', label: 'Active' },
        { value: 'false', label: 'Inactive' }
      ];
    }
  }
};
</script>