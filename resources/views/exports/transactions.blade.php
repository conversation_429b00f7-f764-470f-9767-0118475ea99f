<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Transactions Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f4f4f4;
        }
        .text-right {
            text-align: right;
        }
        .header {
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            color: #333;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Transactions Report</h1>
        <p>Generated on: {{ now()->format('j M Y H:i:s') }}</p>
        @if($startDate || $endDate)
            <p>Period: {{ $startDate ? \Carbon\Carbon::parse($startDate)->format('j M Y') : 'Start' }} - {{ $endDate ? \Carbon\Carbon::parse($endDate)->format('j M Y') : 'End' }}</p>
        @endif
    </div>

    <table>
        <thead>
            <tr>
                <th>Created At</th>
                <th>Reference</th>
                <th>Transaction Type</th>
                <th>Currency</th>
                <th>Debit</th>
                <th>Credit</th>
                <th>Bank</th>
                <th>Account Balance</th>
                <th>Customer</th>
                <th>Created By</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            @foreach($transactions as $transaction)
                <tr>
                    <td>{{ $transaction->created_at }}</td>
                    <td>
                        @php
                            $reference = null;
                            if ($transaction->transactionable_type === 'App\Models\CurrencyOrder' && $transaction->transactionable) {
                                $reference = $transaction->transactionable->reference;
                            } elseif ($transaction->currencyOrder) {
                                $reference = $transaction->currencyOrder->reference;
                            }
                        @endphp
                        {{ $reference }}
                    </td>
                    <td>{{ $transaction->transactionType->name }}</td>
                    <td>{{ $transaction->currency->code }}</td>
                    <td class="text-right">{{ $transaction->debit ? number_format($transaction->debit, 2) : '' }}</td>
                    <td class="text-right">{{ $transaction->credit ? number_format($transaction->credit, 2) : '' }}</td>
                    <td>{{ $transaction->transactionable_type === 'App\Models\Bank' ? $transaction->transactionable->name : '' }}</td>
                    <td class="text-right">{{ $transaction->account_balance ? number_format($transaction->account_balance, 2) : '' }}</td>
                    <td>{{ $transaction->currencyOrder ? $transaction->currencyOrder->customer->name : 'Internal Transfer' }}</td>
                    <td>{{ $transaction->createdBy->name }}</td>
                    <td>{{ $transaction->transactionStatus->name }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>
</body>
</html> 