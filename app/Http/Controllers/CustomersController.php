<?php

namespace App\Http\Controllers;

use App\Enums\CustomerTypeEnum;
use App\Exports\CustomersExport;
use App\Exports\CustomerStatementExport;
use App\Models\CurrencyOrder;
use App\Models\CurrencyOrderStatus;
use App\Models\Currency;
use App\Models\Customer;
use App\Models\Transaction;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;
use Maatwebsite\Excel\Facades\Excel;
use Barryvdh\DomPDF\Facade\Pdf;

class CustomersController extends Controller
{
    public function index(): Response
    {
        $sort = request()->input('sort', 'created_at');
        $direction = request()->input('direction', 'desc');
        $perPage = request()->input('perPage', 10);

        return Inertia::render('Customers/Index', [
            'filters' => request()->only('name', 'code', 'agent', 'credit_limit'),
            'sort' => [
                'field' => $sort,
                'direction' => $direction,
            ],
            'customers' => Customer::query()
                ->where('customer_type', '!=', CustomerTypeEnum::AGENT)
                ->with(['agent', 'referral'])
                ->orderBy($sort, $direction)
                ->filter(request()->only('name', 'code', 'agent', 'credit_limit'))
                ->paginate($perPage)
                ->withQueryString()
                ->through(fn ($customer) => [
                    'id' => $customer->id,
                    'name' => $customer->name,
                    'code' => $customer->code,
                    'credit_limit' => $customer->credit_limit,
                    'credit_amount' => $customer->credit_amount,
                    'is_active' => $customer->is_active,
                    'remarks' => $customer->remarks,
                    'agent' => $customer->agent ? [
                        'name' => $customer->agent->name,
                    ] : null,
                    'referral' => $customer->referral ? [
                        'name' => $customer->referral->name,
                    ] : null,
                    'deleted_at' => $customer->deleted_at,
                    'created_at' => $customer->created_at,
                ]),
        ]);
    }

    public function all()
    {
        return response()->json(
            Customer::where('is_active', true)
                ->where('customer_type', '!=', CustomerTypeEnum::AGENT)
                ->get()
        );
    }

    public function store(): RedirectResponse
    {
        Request::validate([
            'name' => ['required', 'string', 'max:255'],
            'code' => ['required', 'string', 'unique:customers'],
            'credit_limit' => ['required', 'numeric', 'between:0,99999999.99'],
            'is_active' => ['required', 'boolean'],
            'remarks' => ['nullable', 'string'],
            'agent_id' => ['nullable', 'exists:users,id'],
            'referral_id' => ['nullable', 'exists:customers,id'],
        ]);

        $customer = new Customer;
        $customer->fill(Request::only(
            'name',
            'code',
            'credit_limit',
            'is_active',
            'remarks'
        ));
        $customer->customer_type = CustomerTypeEnum::CUSTOMER;
        $customer->agent()->associate(Request::get('agent_id'));
        $customer->referral()->associate(Request::get('referral_id'));
        $customer->save();

        return Redirect::route('customers')->with('success', 'Customer created successfully.');
    }

    public function edit(Customer $customer): Response
    {
        return Inertia::render('Customers/Edit', [
            'customer' => [
                'id' => $customer->id,
                'name' => $customer->name,
                'code' => $customer->code,
                'credit_limit' => $customer->credit_limit,
                'credit_amount' => $customer->credit_amount,
                'is_active' => $customer->is_active,
                'remarks' => $customer->remarks,
                'agent_id' => $customer->agent_id,
                'referral_id' => $customer->referral_id,
                'deleted_at' => $customer->deleted_at,
            ],
        ]);
    }

    public function update(Customer $customer): RedirectResponse
    {
        Request::validate([
            'name' => ['required', 'string', 'max:255'],
            'code' => ['required', 'string', Rule::unique('customers')->ignore($customer->id)],
            'credit_limit' => ['required', 'numeric', 'between:0,99999999.99'],
            'is_active' => ['required', 'boolean'],
            'remarks' => ['nullable', 'string'],
            'agent_id' => ['nullable', 'exists:users,id'],
            'referral_id' => ['nullable', 'exists:customers,id'],
        ]);

        $customer->fill(Request::only(
            'name',
            'code',
            'credit_limit',
            'is_active',
            'remarks'
        ));
        $customer->agent()->associate(Request::get('agent_id'));
        $customer->referral()->associate(Request::get('referral_id'));
        $customer->save();

        return Redirect::back()->with('success', 'Customer updated successfully.');
    }

    public function destroy(Customer $customer): RedirectResponse
    {
        if ($customer->transactions()->exists()) {
            return Redirect::back()->with('error', 'Customer cannot be deleted because it has associated transactions.');
        }

        if ($customer->currencyOrders()->exists()) {
            return Redirect::back()->with('error', 'Customer cannot be deleted because it has associated currency orders.');
        }

        $customer->delete();

        return Redirect::route('customers')->with('success', 'Customer deleted successfully.');
    }

    public function export()
    {
        $sort = request()->input('sort', 'created_at');
        $direction = request()->input('direction', 'desc');
        $filters = request()->only('name', 'code', 'agent', 'credit_limit');

        $filename = 'Customers-'.now()->format('Y-m-d').'.csv';

        return Excel::download(
            new CustomersExport($filters, $sort, $direction),
            $filename,
            \Maatwebsite\Excel\Excel::CSV
        );
    }

    public function customerTransactions(Customer $customer)
    {
        $query = Transaction::query()
            ->with(['currency', 'transactionType', 'bank', 'createdBy'])
            ->whereHasMorph('transactionable', [Customer::class], function ($q) use ($customer) {
                $q->where('id', $customer->id);
            })
            ->whereHas('transactionType', function ($q) {
                $q->where('value', 'customer');
            })
            ->when(request('search'), function ($query, $search) {
                $query->where(function ($query) use ($search) {
                    $query->where('debit', 'like', "%{$search}%")
                        ->orWhere('credit', 'like', "%{$search}%");
                });
            });

        if (request('sort') && request('direction')) {
            $query->orderBy(request('sort'), request('direction'));
        } else {
            $query->latest();
        }

        $perPage = request('perPage', 10);
        $transactions = $query->paginate($perPage);

        $data = [];
        foreach ($transactions as $transaction) {
            $data[] = [
                'id' => $transaction->id,
                'reference' => $transaction->reference,
                'transaction_type' => $transaction->transactionType->name,
                'debit_credit' => $transaction->debit > 0 ? 'Debit' : 'Credit',
                'currency' => [
                    'code' => $transaction->currency->code,
                    'name' => $transaction->currency->name,
                    'photo' => $transaction->currency->photo,
                ],
                'amount' => $transaction->debit > 0 ? $transaction->debit : $transaction->credit,
                'bank' => $transaction->bank ? [
                    'name' => $transaction->bank->name,
                ] : null,
                'bank_balance' => $transaction->account_balance,
                'created_by' => $transaction->createdBy->name,
                'created_at' => $transaction->created_at,
            ];
        }

        return response()->json([
            'data' => $data,
            'meta' => [
                'current_page' => $transactions->currentPage(),
                'from' => $transactions->firstItem() ?? 0,
                'to' => $transactions->lastItem() ?? 0,
                'total' => $transactions->total(),
                'last_page' => $transactions->lastPage() ?: 1,
                'per_page' => $transactions->perPage(),
            ],
        ]);
    }

    public function customerCurrencyOrders(Customer $customer)
    {
        $query = CurrencyOrder::query()
            ->with(['customer', 'inCurrency', 'outCurrency', 'transactions', 'currencyOrderType', 'currencyOrderStatus', 'createdBy'])
            ->where('customer_id', $customer->id)
            ->when(request('search'), function ($query, $search) {
                $query->where(function ($query) use ($search) {
                    $query->where('reference', 'like', "%{$search}%");
                });
            });

        if (request('sort') && request('direction')) {
            $query->orderBy(request('sort'), request('direction'));
        } else {
            $query->latest();
        }

        $perPage = request('perPage', 10);
        $orders = $query->paginate($perPage);

        $data = [];
        foreach ($orders as $order) {
            $data[] = [
                'id' => $order->id,
                'created_at' => $order->created_at,
                'reference' => $order->reference,
                'currency_order_type' => $order->currencyOrderType->name,
                'in_currency' => $order->inCurrency ? [
                    'code' => $order->inCurrency->code,
                    'name' => $order->inCurrency->name,
                    'photo' => $order->inCurrency->photo,
                ] : null,
                'out_currency' => $order->outCurrency ? [
                    'code' => $order->outCurrency->code,
                    'name' => $order->outCurrency->name,
                    'photo' => $order->outCurrency->photo,
                ] : null,
                'receivable_amount' => number_format($order->receivable_amount, 3, '.', ''),
                'fulfilled_receivable_amount' => number_format($order->transactions->sum('debit'), 3, '.', ''),
                'exchange_rate' => number_format($order->exchange_rate, 3, '.', ''),
                'payable_amount' => number_format($order->payable_amount, 3, '.', ''),
                'fulfilled_payable_amount' => number_format($order->transactions->sum('credit'), 3, '.', ''),
                'commission' => $order->currencyOrderType->value === 'po' ? $order->commission : null,
                'commission_myr' => $order->currencyOrderType->value === 'po' ? $order->commission_myr : null,
                'profit_loss' => number_format($order->profit_loss ?? 0, 3, '.', ''),
                'processing_fee' => $order->processing_fee ? number_format($order->processing_fee, 3, '.', '') : null,
                'status' => $order->currencyOrderStatus->name,
                'customer' => $order->customer ? $order->customer->name : null,
                'created_by' => $order->createdBy ? $order->createdBy->name : null,
                'status_timestamps' => $order->status_timestamps,
            ];
        }

        return response()->json([
            'data' => $data,
            'meta' => [
                'current_page' => $orders->currentPage(),
                'from' => $orders->firstItem() ?? 0,
                'to' => $orders->lastItem() ?? 0,
                'total' => $orders->total(),
                'last_page' => $orders->lastPage() ?: 1,
                'per_page' => $orders->perPage(),
            ],
        ]);
    }

    public function customerAudits(Customer $customer)
    {
        $query = $customer->audits()
            ->with('user')
            ->when(request('sort') && request('direction'), function ($query) {
                $query->orderBy(request('sort'), request('direction'));
            }, function ($query) {
                $query->latest();
            });

        $perPage = request('perPage', 10);
        $audits = $query->paginate($perPage);

        return response()->json([
            'data' => $audits->map(function ($audit) {
                return [
                    'id' => $audit->id,
                    'event' => $audit->event,
                    'old_values' => $audit->old_values,
                    'new_values' => $audit->new_values,
                    'user' => $audit->user?->name ?? 'System',
                    'created_at' => $audit->created_at,
                ];
            }),
            'meta' => [
                'current_page' => $audits->currentPage(),
                'from' => $audits->firstItem() ?? 0,
                'to' => $audits->lastItem() ?? 0,
                'total' => $audits->total(),
                'last_page' => $audits->lastPage(),
                'per_page' => $audits->perPage(),
            ],
        ]);
    }

    public function statement(): Response
    {
        $sort = request()->input('sort', 'created_at');
        $direction = request()->input('direction', 'asc');
        $perPage = request()->input('perPage', 10);
        $filters = request()->only('customer_name', 'customer_code', 'start_date', 'end_date');

        $query = CurrencyOrder::query()
            ->with(['customer', 'inCurrency', 'outCurrency', 'transactions', 'currencyOrderType'])
            ->when($filters['customer_name'] ?? null, function ($query, $customerName) {
                $query->whereHas('customer', function ($q) use ($customerName) {
                    $q->where('name', 'like', '%'.$customerName.'%');
                });
            })
            ->when($filters['customer_code'] ?? null, function ($query, $customerCode) {
                $query->whereHas('customer', function ($q) use ($customerCode) {
                    $q->where('code', 'like', '%'.$customerCode.'%');
                });
            })
            ->when($filters['start_date'] ?? null, function ($query, $startDate) {
                $query->whereDate('created_at', '>=', $startDate);
            })
            ->when($filters['end_date'] ?? null, function ($query, $endDate) {
                $query->whereDate('created_at', '<=', $endDate);
            })
            ->orderBy($sort, $direction);

        $currencyOrders = $query->paginate($perPage)->withQueryString();

        // Get debtor/creditor data based on the same filters
        $debtorCreditorData = $this->getStatementDebtorCreditor($filters);

        return Inertia::render('Customers/Statement/Index', [
            'filters' => $filters,
            'sort' => [
                'field' => $sort,
                'direction' => $direction,
            ],
            'customers' => Customer::where('is_active', true)
                ->where('customer_type', '!=', CustomerTypeEnum::AGENT)
                ->get(['id', 'name', 'code']),
            'currencyOrders' => $currencyOrders->through(function ($order, $index) use ($currencyOrders) {
                return [
                    'id' => $order->id,
                    'index' => $currencyOrders->firstItem() + $index,
                    'created_at' => $order->created_at,
                    'reference' => $order->reference,
                    'in_currency' => $order->inCurrency ? [
                        'code' => $order->inCurrency->code,
                        'name' => $order->inCurrency->name,
                        'photo' => $order->inCurrency->photo,
                    ] : null,
                    'out_currency' => $order->outCurrency ? [
                        'code' => $order->outCurrency->code,
                        'name' => $order->outCurrency->name,
                        'photo' => $order->outCurrency->photo,
                    ] : null,
                    'receivable_amount' => number_format($order->receivable_amount, 2, '.', ','),
                    'fulfilled_receivable_amount' => number_format($order->transactions->sum('debit'), 2, '.', ','),
                    'exchange_rate' => number_format($order->exchange_rate, 2, '.', ','),
                    'payable_amount' => number_format($order->payable_amount, 2, '.', ','),
                    'fulfilled_payable_amount' => number_format($order->transactions->sum('credit'), 2, '.', ','),
                    'balance_buy' => number_format($order->transactions->sum('debit'), 2, '.', ','),
                    'balance_sell' => number_format($order->transactions->sum('credit'), 2, '.', ','),
                    'receivable_fulfilled_match' => (float)$order->receivable_amount === (float)$order->transactions->sum('debit'),
                    'payable_fulfilled_match' => (float)$order->payable_amount === (float)$order->transactions->sum('credit'),
                    'currency_order_type' => $order->currencyOrderType ? $order->currencyOrderType->value : null,
                    'customer' => $order->customer ? [
                        'id' => $order->customer->id,
                        'name' => $order->customer->name,
                        'code' => $order->customer->code,
                    ] : null,
                ];
            }),
            'debtors' => $debtorCreditorData['debtors'],
            'creditors' => $debtorCreditorData['creditors'],
        ]);
    }

    public function exportStatement()
    {
        $sort = request('sort', 'created_at');
        $direction = request('direction', 'asc');

        // Get all request parameters and filter out empty values
        $filters = array_filter(request()->all(), function ($value) {
            return $value !== null && $value !== '';
        });

        // Keep only the filter fields we want
        $filters = array_intersect_key($filters, array_flip([
            'customer_name', 'customer_code', 'start_date', 'end_date',
        ]));

        $filename = 'Customer-Statement-'.now()->format('Y-m-d').'.csv';

        return Excel::download(
            new CustomerStatementExport($filters, $sort, $direction),
            $filename,
            \Maatwebsite\Excel\Excel::CSV
        );
    }

    public function exportStatementPdf()
    {
        $sort = request('sort', 'created_at');
        $direction = request('direction', 'asc');

        // Get all request parameters and filter out empty values
        $filters = array_filter(request()->all(), function ($value) {
            return $value !== null && $value !== '';
        });

        // Keep only the filter fields we want
        $filters = array_intersect_key($filters, array_flip([
            'customer_name', 'customer_code', 'start_date', 'end_date',
        ]));

        $query = CurrencyOrder::query()
            ->with(['customer', 'inCurrency', 'outCurrency', 'transactions', 'currencyOrderType'])
            ->when($filters['customer_name'] ?? null, function ($query, $customerName) {
                $query->whereHas('customer', function ($q) use ($customerName) {
                    $q->where('name', 'like', '%'.$customerName.'%');
                });
            })
            ->when($filters['customer_code'] ?? null, function ($query, $customerCode) {
                $query->whereHas('customer', function ($q) use ($customerCode) {
                    $q->where('code', 'like', '%'.$customerCode.'%');
                });
            })
            ->when($filters['start_date'] ?? null, function ($query, $startDate) {
                $query->whereDate('created_at', '>=', $startDate);
            })
            ->when($filters['end_date'] ?? null, function ($query, $endDate) {
                $query->whereDate('created_at', '<=', $endDate);
            })
            ->orderBy($sort, $direction);

        $currencyOrders = $query->get();

        $pdf = Pdf::loadView('exports.customer-statement', [
            'currencyOrders' => $currencyOrders,
            'filters' => $filters,
        ]);

        return $pdf->download('Customer-Statement-'.now()->format('Y-m-d').'.pdf');
    }

    public function customerDebtorCreditor(Customer $customer)
    {
        // Get completed orders for this customer
        $completedStatusIds = CurrencyOrderStatus::whereNotIn('value', ['pending', 'cancelled'])
            ->pluck('id')
            ->toArray();

        $completedOrders = CurrencyOrder::whereIn('currency_order_status_id', $completedStatusIds)
            ->where('customer_id', $customer->id)
            ->whereHas('currencyOrderType', function($query) {
                $query->whereIn('value', ['po', 'tpp', 'tpr']);
            })
            ->with(['inCurrency', 'outCurrency', 'currencyOrderType', 'transactions'])
            ->get();

        // Initialize collections
        $debtors = collect();
        $creditors = collect();

        // Group orders by currency for easier balance calculation
        $currencyBalances = [];

        foreach ($completedOrders as $order) {
            // Process PO (Purchase Order) type
            if ($order->currencyOrderType->value === 'po') {
                // Process In Currency (Receivable)
                if ($order->inCurrency) {
                    $currencyId = $order->inCurrency->id;

                    if (!isset($currencyBalances[$currencyId])) {
                        $currencyBalances[$currencyId] = [
                            'currency' => $order->inCurrency,
                            'po_receivable' => 0,
                            'po_payable' => 0,
                            'tpr_fulfilled' => 0,
                            'tpp_fulfilled' => 0
                        ];
                    }

                    $currencyBalances[$currencyId]['po_receivable'] += $order->receivable_amount;
                }

                // Process Out Currency (Payable)
                if ($order->outCurrency) {
                    $currencyId = $order->outCurrency->id;

                    if (!isset($currencyBalances[$currencyId])) {
                        $currencyBalances[$currencyId] = [
                            'currency' => $order->outCurrency,
                            'po_receivable' => 0,
                            'po_payable' => 0,
                            'tpr_fulfilled' => 0,
                            'tpp_fulfilled' => 0
                        ];
                    }

                    $currencyBalances[$currencyId]['po_payable'] += $order->payable_amount;
                }
            }

            // Process TPR (Transfer Payment Receivable) type
            else if ($order->currencyOrderType->value === 'tpr') {
                if ($order->inCurrency) {
                    $currencyId = $order->inCurrency->id;

                    if (!isset($currencyBalances[$currencyId])) {
                        $currencyBalances[$currencyId] = [
                            'currency' => $order->inCurrency,
                            'po_receivable' => 0,
                            'po_payable' => 0,
                            'tpr_fulfilled' => 0,
                            'tpp_fulfilled' => 0
                        ];
                    }

                    // Sum up fulfilled amount from transactions
                    $fulfilledAmount = $order->transactions->sum('debit');
                    $currencyBalances[$currencyId]['tpr_fulfilled'] += $fulfilledAmount;
                }
            }

            // Process TPP (Transfer Payment Payable) type
            else if ($order->currencyOrderType->value === 'tpp') {
                if ($order->outCurrency) {
                    $currencyId = $order->outCurrency->id;

                    if (!isset($currencyBalances[$currencyId])) {
                        $currencyBalances[$currencyId] = [
                            'currency' => $order->outCurrency,
                            'po_receivable' => 0,
                            'po_payable' => 0,
                            'tpr_fulfilled' => 0,
                            'tpp_fulfilled' => 0
                        ];
                    }

                    // Sum up fulfilled amount from transactions
                    $fulfilledAmount = $order->transactions->sum('credit');
                    $currencyBalances[$currencyId]['tpp_fulfilled'] += $fulfilledAmount;
                }
            }
        }

        // Process final balances
        foreach ($currencyBalances as $currencyId => $currencyData) {
            // Calculate net receivable (PO receivable - TPR fulfilled)
            $netReceivable = $currencyData['po_receivable'] - $currencyData['tpr_fulfilled'];

            // Calculate net payable (PO payable - TPP fulfilled)
            $netPayable = $currencyData['po_payable'] - $currencyData['tpp_fulfilled'];

            // If we have a positive net receivable, customer owes us (debtor)
            if ($netReceivable > 0) {
                $debtors->push([
                    'id' => $currencyId,
                    'code' => $currencyData['currency']->code,
                    'name' => $currencyData['currency']->name,
                    'photo' => $currencyData['currency']->photo,
                    'amount' => $netReceivable,
                ]);
            }

            // If we have a positive net payable, we owe the customer (creditor)
            if ($netPayable > 0) {
                $creditors->push([
                    'id' => $currencyId,
                    'code' => $currencyData['currency']->code,
                    'name' => $currencyData['currency']->name,
                    'photo' => $currencyData['currency']->photo,
                    'amount' => $netPayable,
                ]);
            }
        }

        // Sort by amount (largest first)
        $debtors = $debtors->sortByDesc('amount')->values();
        $creditors = $creditors->sortByDesc('amount')->values();

        return response()->json([
            'debtors' => $debtors,
            'creditors' => $creditors,
        ]);
    }

    private function getStatementDebtorCreditor($filters)
    {
        // Get completed orders based on filters
        $completedStatusIds = CurrencyOrderStatus::whereNotIn('value', ['pending', 'cancelled'])
            ->pluck('id')
            ->toArray();

        $query = CurrencyOrder::whereIn('currency_order_status_id', $completedStatusIds)
            ->whereHas('currencyOrderType', function($query) {
                $query->whereIn('value', ['po', 'tpp', 'tpr']);
            })
            ->with(['customer', 'inCurrency', 'outCurrency', 'currencyOrderType', 'transactions'])
            ->when($filters['customer_name'] ?? null, function ($query, $customerName) {
                $query->whereHas('customer', function ($q) use ($customerName) {
                    $q->where('name', 'like', '%'.$customerName.'%');
                });
            })
            ->when($filters['customer_code'] ?? null, function ($query, $customerCode) {
                $query->whereHas('customer', function ($q) use ($customerCode) {
                    $q->where('code', 'like', '%'.$customerCode.'%');
                });
            })
            ->when($filters['start_date'] ?? null, function ($query, $startDate) {
                $query->whereDate('created_at', '>=', $startDate);
            })
            ->when($filters['end_date'] ?? null, function ($query, $endDate) {
                $query->whereDate('created_at', '<=', $endDate);
            });

        $completedOrders = $query->get();

        // Initialize collections
        $debtors = collect();
        $creditors = collect();

        // Group orders by currency for easier balance calculation
        $currencyBalances = [];

        foreach ($completedOrders as $order) {
            // Process PO (Purchase Order) type
            if ($order->currencyOrderType->value === 'po') {
                // Process In Currency (Receivable)
                if ($order->inCurrency) {
                    $currencyId = $order->inCurrency->id;

                    if (!isset($currencyBalances[$currencyId])) {
                        $currencyBalances[$currencyId] = [
                            'currency' => $order->inCurrency,
                            'po_receivable' => 0,
                            'po_payable' => 0,
                            'tpr_fulfilled' => 0,
                            'tpp_fulfilled' => 0
                        ];
                    }

                    $currencyBalances[$currencyId]['po_receivable'] += $order->receivable_amount;
                }

                // Process Out Currency (Payable)
                if ($order->outCurrency) {
                    $currencyId = $order->outCurrency->id;

                    if (!isset($currencyBalances[$currencyId])) {
                        $currencyBalances[$currencyId] = [
                            'currency' => $order->outCurrency,
                            'po_receivable' => 0,
                            'po_payable' => 0,
                            'tpr_fulfilled' => 0,
                            'tpp_fulfilled' => 0
                        ];
                    }

                    $currencyBalances[$currencyId]['po_payable'] += $order->payable_amount;
                }
            }

            // Process TPR (Transfer Payment Receivable) type
            else if ($order->currencyOrderType->value === 'tpr') {
                if ($order->inCurrency) {
                    $currencyId = $order->inCurrency->id;

                    if (!isset($currencyBalances[$currencyId])) {
                        $currencyBalances[$currencyId] = [
                            'currency' => $order->inCurrency,
                            'po_receivable' => 0,
                            'po_payable' => 0,
                            'tpr_fulfilled' => 0,
                            'tpp_fulfilled' => 0
                        ];
                    }

                    // Sum up fulfilled amount from transactions
                    $fulfilledAmount = $order->transactions->sum('debit');
                    $currencyBalances[$currencyId]['tpr_fulfilled'] += $fulfilledAmount;
                }
            }

            // Process TPP (Transfer Payment Payable) type
            else if ($order->currencyOrderType->value === 'tpp') {
                if ($order->outCurrency) {
                    $currencyId = $order->outCurrency->id;

                    if (!isset($currencyBalances[$currencyId])) {
                        $currencyBalances[$currencyId] = [
                            'currency' => $order->outCurrency,
                            'po_receivable' => 0,
                            'po_payable' => 0,
                            'tpr_fulfilled' => 0,
                            'tpp_fulfilled' => 0
                        ];
                    }

                    // Sum up fulfilled amount from transactions
                    $fulfilledAmount = $order->transactions->sum('credit');
                    $currencyBalances[$currencyId]['tpp_fulfilled'] += $fulfilledAmount;
                }
            }
        }

        // Process final balances
        foreach ($currencyBalances as $currencyId => $currencyData) {
            // Calculate net receivable (PO receivable - TPR fulfilled)
            $netReceivable = $currencyData['po_receivable'] - $currencyData['tpr_fulfilled'];

            // Calculate net payable (PO payable - TPP fulfilled)
            $netPayable = $currencyData['po_payable'] - $currencyData['tpp_fulfilled'];

            // If we have a positive net receivable, customer owes us (debtor)
            if ($netReceivable > 0) {
                $debtors->push([
                    'id' => $currencyId,
                    'code' => $currencyData['currency']->code,
                    'name' => $currencyData['currency']->name,
                    'photo' => $currencyData['currency']->photo,
                    'amount' => $netReceivable,
                ]);
            }

            // If we have a positive net payable, we owe the customer (creditor)
            if ($netPayable > 0) {
                $creditors->push([
                    'id' => $currencyId,
                    'code' => $currencyData['currency']->code,
                    'name' => $currencyData['currency']->name,
                    'photo' => $currencyData['currency']->photo,
                    'amount' => $netPayable,
                ]);
            }
        }

        // Sort by amount (largest first)
        $debtors = $debtors->sortByDesc('amount')->values();
        $creditors = $creditors->sortByDesc('amount')->values();

        return [
            'debtors' => $debtors,
            'creditors' => $creditors,
        ];
    }
}
