<?php

namespace App\Http\Controllers;

use App\Models\Bank;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\DB;

class DebtorCreditorController extends Controller
{
    public function overall()
    {
        $dateFilter = request()->input('date');

        // Use database aggregation instead of PHP processing for better performance
        $customerCurrencyBalances = $this->getOptimizedBalances($dateFilter);

        $cashDebtors = collect();
        $coinDebtors = collect();
        $cashCreditors = collect();
        $coinCreditors = collect();

        // Process final balances
        foreach ($customerCurrencyBalances as $customerId => $customerData) {
            foreach ($customerData['currencies'] as $currencyId => $currencyData) {
                // Calculate net receivable balance (PO receivable - TPR fulfilled)
                $netReceivable = $currencyData['po_receivable'] - $currencyData['tpr_fulfilled'];

                if ($netReceivable != 0) {
                    $currencyInfo = [
                        'id' => $currencyId,
                        'code' => $currencyData['currency']->code,
                        'photo' => $currencyData['currency']->photo,
                        'name' => $currencyData['currency']->name,
                        'customers' => [[
                            'id' => $customerId,
                            'name' => $customerData['name'],
                            'amount' => $netReceivable
                        ]]
                    ];

                    if ($netReceivable > 0) {
                        // Positive balance goes to debtors
                        if ($currencyData['currency']->is_crypto) {
                            $coinDebtors->push(array_merge($currencyInfo, ['total_amount' => $netReceivable]));
                        } else {
                            $cashDebtors->push(array_merge($currencyInfo, ['total_amount' => $netReceivable]));
                        }
                    } else {
                        // Negative balance goes to creditors
                        if ($currencyData['currency']->is_crypto) {
                            $coinCreditors->push(array_merge($currencyInfo, ['total_amount' => $netReceivable]));
                        } else {
                            $cashCreditors->push(array_merge($currencyInfo, ['total_amount' => $netReceivable]));
                        }
                    }
                }

                // Calculate net payable balance (PO payable - TPP fulfilled)
                $netPayable = $currencyData['po_payable'] - $currencyData['tpp_fulfilled'];

                if ($netPayable != 0) {
                    $currencyInfo = [
                        'id' => $currencyId,
                        'code' => $currencyData['currency']->code,
                        'photo' => $currencyData['currency']->photo,
                        'name' => $currencyData['currency']->name,
                        'customers' => [[
                            'id' => $customerId,
                            'name' => $customerData['name'],
                            'amount' => -$netPayable // Negative for creditors
                        ]]
                    ];

                    if ($netPayable > 0) {
                        // Positive balance goes to creditors
                        if ($currencyData['currency']->is_crypto) {
                            $coinCreditors->push(array_merge($currencyInfo, ['total_amount' => -$netPayable]));
                        } else {
                            $cashCreditors->push(array_merge($currencyInfo, ['total_amount' => -$netPayable]));
                        }
                    } else {
                        // Negative balance goes to debtors
                        if ($currencyData['currency']->is_crypto) {
                            $coinDebtors->push(array_merge($currencyInfo, ['total_amount' => abs($netPayable)]));
                        } else {
                            $cashDebtors->push(array_merge($currencyInfo, ['total_amount' => abs($netPayable)]));
                        }
                    }
                }
            }
        }

        // Group by currency and sum amounts
        $cashDebtors = $this->groupByCurrency($cashDebtors);
        $cashCreditors = $this->groupByCurrency($cashCreditors);
        $coinDebtors = $this->groupByCurrency($coinDebtors);
        $coinCreditors = $this->groupByCurrency($coinCreditors);

        return inertia('DebtorCreditor/Overall/Index', [
            'filters' => request()->only([
                'search',
                'perPage',
                'date',
            ]),
            'cash_debtors' => $cashDebtors,
            'cash_creditors' => $cashCreditors,
            'coin_debtors' => $coinDebtors,
            'coin_creditors' => $coinCreditors,
            'date_filter' => $dateFilter ? date('d M Y', strtotime($dateFilter)) : null,
        ]);
    }

    public function daily()
    {
        $today = date('Y-m-d');
        $startDate = $today . ' 00:00:00';
        $endDate = $today . ' 23:59:59';

        // Use optimized database aggregation instead of PHP processing for better performance
        $customerCurrencyBalances = $this->getOptimizedDailyBalances($startDate, $endDate);

        $cashDebtors = collect();
        $coinDebtors = collect();
        $cashCreditors = collect();
        $coinCreditors = collect();

        // Process final balances
        foreach ($customerCurrencyBalances as $customerId => $customerData) {
            foreach ($customerData['currencies'] as $currencyId => $currencyData) {
                // Calculate net receivable balance (PO receivable - TPR fulfilled)
                $netReceivable = $currencyData['po_receivable'] - $currencyData['tpr_fulfilled'];

                if ($netReceivable != 0) {
                    $photoUrl = $currencyData['currency']->photo_path
                        ? \Illuminate\Support\Facades\URL::route('image', [
                            'path' => $currencyData['currency']->photo_path,
                            'w' => 40,
                            'h' => 40,
                            'fit' => 'crop',
                        ])
                        : '/images/currencies/BLANK.png';

                    $currencyInfo = [
                        'id' => $currencyId,
                        'code' => $currencyData['currency']->code,
                        'photo' => $photoUrl,
                        'name' => $currencyData['currency']->name,
                        'customers' => [[
                            'id' => $customerId,
                            'name' => $customerData['name'],
                            'amount' => $netReceivable
                        ]]
                    ];

                    if ($netReceivable > 0) {
                        // Positive balance goes to debtors
                        if ($currencyData['currency']->is_crypto) {
                            $coinDebtors->push(array_merge($currencyInfo, ['total_amount' => $netReceivable]));
                        } else {
                            $cashDebtors->push(array_merge($currencyInfo, ['total_amount' => $netReceivable]));
                        }
                    } else {
                        // Negative balance goes to creditors
                        if ($currencyData['currency']->is_crypto) {
                            $coinCreditors->push(array_merge($currencyInfo, ['total_amount' => $netReceivable]));
                        } else {
                            $cashCreditors->push(array_merge($currencyInfo, ['total_amount' => $netReceivable]));
                        }
                    }
                }

                // Calculate net payable balance (PO payable - TPP fulfilled)
                $netPayable = $currencyData['po_payable'] - $currencyData['tpp_fulfilled'];

                if ($netPayable != 0) {
                    $photoUrl = $currencyData['currency']->photo_path
                        ? \Illuminate\Support\Facades\URL::route('image', [
                            'path' => $currencyData['currency']->photo_path,
                            'w' => 40,
                            'h' => 40,
                            'fit' => 'crop',
                        ])
                        : '/images/currencies/BLANK.png';

                    $currencyInfo = [
                        'id' => $currencyId,
                        'code' => $currencyData['currency']->code,
                        'photo' => $photoUrl,
                        'name' => $currencyData['currency']->name,
                        'customers' => [[
                            'id' => $customerId,
                            'name' => $customerData['name'],
                            'amount' => -$netPayable // Negative for creditors
                        ]]
                    ];

                    if ($netPayable > 0) {
                        // Positive balance goes to creditors
                        if ($currencyData['currency']->is_crypto) {
                            $coinCreditors->push(array_merge($currencyInfo, ['total_amount' => -$netPayable]));
                        } else {
                            $cashCreditors->push(array_merge($currencyInfo, ['total_amount' => -$netPayable]));
                        }
                    } else {
                        // Negative balance goes to debtors
                        if ($currencyData['currency']->is_crypto) {
                            $coinDebtors->push(array_merge($currencyInfo, ['total_amount' => abs($netPayable)]));
                        } else {
                            $cashDebtors->push(array_merge($currencyInfo, ['total_amount' => abs($netPayable)]));
                        }
                    }
                }
            }
        }

        // Group by currency and sum amounts
        $cashDebtors = $this->groupByCurrency($cashDebtors);
        $cashCreditors = $this->groupByCurrency($cashCreditors);
        $coinDebtors = $this->groupByCurrency($coinDebtors);
        $coinCreditors = $this->groupByCurrency($coinCreditors);

        return inertia('DebtorCreditor/Daily/Index', [
            'cash_debtors' => $cashDebtors,
            'cash_creditors' => $cashCreditors,
            'coin_debtors' => $coinDebtors,
            'coin_creditors' => $coinCreditors,
        ]);
    }

     public function customer()
    {
        $dateFilter = request()->input('date');
        $customerNameFilter = request()->input('customer_name');
        $customerCodeFilter = request()->input('customer_code');

        // Use optimized database aggregation instead of PHP processing for better performance
        $customerCurrencyBalances = $this->getOptimizedCustomerBalances($dateFilter, $customerNameFilter, $customerCodeFilter);

        // Process final balances
        $customers = [];
        foreach ($customerCurrencyBalances as $customerId => $customerData) {
            $customer = [
                'id' => $customerId,
                'name' => $customerData['name'],
                'code' => $customerData['code'],
                'currencies' => []
            ];

            foreach ($customerData['currencies'] as $currencyId => $currencyData) {
                // Calculate net receivable (PO receivable - TPR fulfilled)
                $netReceivable = $currencyData['po_receivable'] - $currencyData['tpr_fulfilled'];

                // Calculate net payable (PO payable - TPP fulfilled)
                $netPayable = $currencyData['po_payable'] - $currencyData['tpp_fulfilled'];

                // Final balance for this currency
                $finalBalance = $netReceivable - $netPayable;

                if ($finalBalance != 0) {
                    $customer['currencies'][$currencyId] = $finalBalance;
                }
            }

            if (!empty($customer['currencies'])) {
                $customers[] = $customer;
            }
        }

        // Get all unique currencies used with optimized query
        $currenciesUsed = $this->getUsedCurrencies($dateFilter, $customerNameFilter, $customerCodeFilter);

        return inertia('DebtorCreditor/ByCustomer/Index', [
            'filters' => request()->only([
                'date',
                'customer_name',
                'customer_code',
            ]),
            'currencies' => $currenciesUsed,
            'customers' => $customers,
            'date_filter' => $dateFilter ? date('d M Y', strtotime($dateFilter)) : null,
        ]);
    }

    public function exportDaily()
    {
        $completedStatusIds = \App\Models\CurrencyOrderStatus::whereNotIn('value', ['pending', 'cancelled'])
            ->pluck('id')
            ->toArray();

        $today = date('Y-m-d');
        $startDate = $today . ' 00:00:00';
        $endDate = $today . ' 23:59:59';

        // Get PO, TPP and TPR orders for today - same query as daily()
        $completedOrders = \App\Models\CurrencyOrder::whereIn('currency_order_status_id', $completedStatusIds)
            ->whereHas('currencyOrderType', function($query) {
                $query->whereIn('value', ['po', 'tpp', 'tpr']);
            })
            ->whereBetween('updated_at', [$startDate, $endDate])
            ->with(['customer', 'inCurrency', 'outCurrency', 'currencyOrderType', 'transactions'])
            ->get();

        // Initialize collections
        $cashDebtors = collect();
        $coinDebtors = collect();
        $cashCreditors = collect();
        $coinCreditors = collect();

        // Group orders by customer and currency - same logic as daily()
        $customerCurrencyBalances = [];

        foreach ($completedOrders as $order) {
                $customerId = $order->customer_id;
                $customerName = $order->customer ? $order->customer->name : 'Unknown Customer';

            if (!isset($customerCurrencyBalances[$customerId])) {
                $customerCurrencyBalances[$customerId] = [
                        'name' => $customerName,
                    'currencies' => []
                ];
            }

            // Handle TPR orders
            if ($order->currencyOrderType->value === 'tpr' && $order->in_currency_id) {
                $fulfilledAmount = $order->transactions()
                    ->whereHas('transactionStatus', function($query) {
                        $query->where('value', '!=', 'cancelled');
                    })
                    ->whereHas('transactionType', function ($query) {
                        $query->where('value', '!=', 'bank_charges');
                    })
                    ->sum('debit');

                if ($fulfilledAmount > 0) {
                    if (!isset($customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id])) {
                        $customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id] = [
                            'currency' => $order->inCurrency,
                            'tpr_fulfilled' => 0,
                            'tpp_fulfilled' => 0,
                            'po_receivable' => 0,
                            'po_payable' => 0
                        ];
                    }
                    $customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id]['tpr_fulfilled'] += $fulfilledAmount;
                }
            }

            // Handle TPP orders
            if ($order->currencyOrderType->value === 'tpp' && $order->out_currency_id) {
                $fulfilledAmount = $order->transactions()
                    ->whereHas('transactionStatus', function($query) {
                        $query->where('value', '!=', 'cancelled');
                    })
                    ->whereHas('transactionType', function ($query) {
                        $query->where('value', '!=', 'bank_charges');
                    })
                    ->sum('credit');

                if ($fulfilledAmount > 0) {
                    if (!isset($customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id])) {
                        $customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id] = [
                            'currency' => $order->outCurrency,
                            'tpr_fulfilled' => 0,
                            'tpp_fulfilled' => 0,
                            'po_receivable' => 0,
                            'po_payable' => 0
                        ];
                    }
                    $customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id]['tpp_fulfilled'] += $fulfilledAmount;
                }
            }

            // Handle PO orders
            if ($order->currencyOrderType->value === 'po') {
                // Calculate fulfilled amounts first
                $fulfilledReceivable = $order->transactions()
                    ->whereHas('transactionStatus', function($query) {
                        $query->where('value', '!=', 'cancelled');
                    })
                    ->whereHas('transactionType', function ($query) {
                        $query->where('value', '!=', 'bank_charges');
                    })
                    ->sum('debit');

                $fulfilledPayable = $order->transactions()
                    ->whereHas('transactionStatus', function($query) {
                        $query->where('value', '!=', 'cancelled');
                    })
                    ->whereHas('transactionType', function ($query) {
                        $query->where('value', '!=', 'bank_charges');
                    })
                    ->sum('credit');

                // Handle receivable amount (in_currency)
                if ($order->in_currency_id && $order->receivable_amount > 0) {
                    $receivableBalance = $order->receivable_amount - $fulfilledReceivable;
                    if ($receivableBalance > 0) {
                        if (!isset($customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id])) {
                            $customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id] = [
                                'currency' => $order->inCurrency,
                                'tpr_fulfilled' => 0,
                                'tpp_fulfilled' => 0,
                                'po_receivable' => 0,
                                'po_payable' => 0
                            ];
                        }
                        $customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id]['po_receivable'] += $receivableBalance;
                    }
                }

                // Handle payable amount (out_currency)
                if ($order->out_currency_id && $order->payable_amount > 0) {
                    $payableBalance = $order->payable_amount - $fulfilledPayable;
                    if ($payableBalance > 0) {
                        if (!isset($customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id])) {
                            $customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id] = [
                                'currency' => $order->outCurrency,
                                'tpr_fulfilled' => 0,
                                'tpp_fulfilled' => 0,
                                'po_receivable' => 0,
                                'po_payable' => 0
                            ];
                        }
                        $customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id]['po_payable'] += $payableBalance;
                    }
                }
            }
        }

        // Process final balances - same logic as daily()
        foreach ($customerCurrencyBalances as $customerId => $customerData) {
            foreach ($customerData['currencies'] as $currencyId => $currencyData) {
                // Calculate net receivable balance (PO receivable - TPR fulfilled)
                $netReceivable = $currencyData['po_receivable'] - $currencyData['tpr_fulfilled'];

                if ($netReceivable != 0) {
                    $currencyInfo = [
                'id' => $currencyId,
                        'code' => $currencyData['currency']->code,
                        'photo' => $currencyData['currency']->photo,
                        'name' => $currencyData['currency']->name,
                        'customers' => [[
                            'id' => $customerId,
                            'name' => $customerData['name'],
                            'amount' => $netReceivable
                        ]]
                    ];

                    if ($netReceivable > 0) {
                        if ($currencyData['currency']->is_crypto) {
                            $coinDebtors->push(array_merge($currencyInfo, ['total_amount' => $netReceivable]));
            } else {
                            $cashDebtors->push(array_merge($currencyInfo, ['total_amount' => $netReceivable]));
                        }
                    } else {
                        if ($currencyData['currency']->is_crypto) {
                            $coinCreditors->push(array_merge($currencyInfo, ['total_amount' => $netReceivable]));
                        } else {
                            $cashCreditors->push(array_merge($currencyInfo, ['total_amount' => $netReceivable]));
                        }
                    }
                }

                // Calculate net payable balance (PO payable - TPP fulfilled)
                $netPayable = $currencyData['po_payable'] - $currencyData['tpp_fulfilled'];

                if ($netPayable != 0) {
                    $currencyInfo = [
                'id' => $currencyId,
                        'code' => $currencyData['currency']->code,
                        'photo' => $currencyData['currency']->photo,
                        'name' => $currencyData['currency']->name,
                        'customers' => [[
                            'id' => $customerId,
                            'name' => $customerData['name'],
                            'amount' => -$netPayable
                        ]]
                    ];

                    if ($netPayable > 0) {
                        if ($currencyData['currency']->is_crypto) {
                            $coinCreditors->push(array_merge($currencyInfo, ['total_amount' => -$netPayable]));
            } else {
                            $cashCreditors->push(array_merge($currencyInfo, ['total_amount' => -$netPayable]));
                        }
                    } else {
                        if ($currencyData['currency']->is_crypto) {
                            $coinDebtors->push(array_merge($currencyInfo, ['total_amount' => abs($netPayable)]));
                        } else {
                            $cashDebtors->push(array_merge($currencyInfo, ['total_amount' => abs($netPayable)]));
                        }
                    }
                }
            }
        }

        // Group by currency and sum amounts
        $cashDebtors = $this->groupByCurrency($cashDebtors);
        $cashCreditors = $this->groupByCurrency($cashCreditors);
        $coinDebtors = $this->groupByCurrency($coinDebtors);
        $coinCreditors = $this->groupByCurrency($coinCreditors);

        // Create CSV data
        $csvData = [];
        $csvData[] = ['Date', date('Y-m-d')];
        $csvData[] = [];

        // Add Cash Debtors section
        if ($cashDebtors->isNotEmpty()) {
        $csvData[] = ['CURRENCY - DEBTORS'];
        foreach ($cashDebtors as $currency) {
                $csvData[] = [
                    $currency['code'],
                    $currency['name'],
                    'Total Amount: ' . number_format(abs($currency['total_amount']), 2)
                ];
            $csvData[] = ['Customer', 'Amount'];

            foreach ($currency['customers'] as $customer) {
                    if ($customer['amount'] > 0) {
                        $csvData[] = [
                            $customer['name'],
                            number_format($customer['amount'], 2)
                        ];
                    }
                }
                $csvData[] = [];
            }
        }

        // Add Cash Creditors section
        if ($cashCreditors->isNotEmpty()) {
        $csvData[] = ['CURRENCY - CREDITORS'];
        foreach ($cashCreditors as $currency) {
                $csvData[] = [
                    $currency['code'],
                    $currency['name'],
                    'Total Amount: ' . number_format($currency['total_amount'], 2)
                ];
            $csvData[] = ['Customer', 'Amount'];

            foreach ($currency['customers'] as $customer) {
                    if ($customer['amount'] < 0) {
                        $csvData[] = [
                            $customer['name'],
                            number_format($customer['amount'], 2)
                        ];
                    }
                }
                $csvData[] = [];
            }
        }

        // Add Coin Debtors section
        if ($coinDebtors->isNotEmpty()) {
        $csvData[] = ['COIN - DEBTORS'];
        foreach ($coinDebtors as $currency) {
                $csvData[] = [
                    $currency['code'],
                    $currency['name'],
                    'Total Amount: ' . number_format(abs($currency['total_amount']), 2)
                ];
            $csvData[] = ['Customer', 'Amount'];

            foreach ($currency['customers'] as $customer) {
                    if ($customer['amount'] > 0) {
                        $csvData[] = [
                            $customer['name'],
                            number_format($customer['amount'], 2)
                        ];
                    }
                }
                $csvData[] = [];
            }
        }

        // Add Coin Creditors section
        if ($coinCreditors->isNotEmpty()) {
        $csvData[] = ['COIN - CREDITORS'];
        foreach ($coinCreditors as $currency) {
                $csvData[] = [
                    $currency['code'],
                    $currency['name'],
                    'Total Amount: ' . number_format($currency['total_amount'], 2)
                ];
            $csvData[] = ['Customer', 'Amount'];

            foreach ($currency['customers'] as $customer) {
                    if ($customer['amount'] < 0) {
                        $csvData[] = [
                            $customer['name'],
                            number_format($customer['amount'], 2)
                        ];
                    }
                }
        $csvData[] = [];
            }
        }

        // Generate CSV file
        $output = fopen('php://temp', 'w');
        foreach ($csvData as $row) {
            fputcsv($output, $row);
        }
        rewind($output);
        $csv = stream_get_contents($output);
        fclose($output);

        return Response::make($csv, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="daily-debtor-creditor-' . date('Y-m-d') . '.csv"',
        ]);
    }

    public function exportOverall()
    {
        $completedStatusIds = \App\Models\CurrencyOrderStatus::whereNotIn('value', ['pending', 'cancelled'])
            ->pluck('id')
            ->toArray();

        $dateFilter = request()->input('date');
        $query = \App\Models\CurrencyOrder::whereIn('currency_order_status_id', $completedStatusIds)
            ->whereHas('currencyOrderType', function($query) {
                $query->whereIn('value', ['po', 'tpp', 'tpr']);
            });

        if ($dateFilter) {
            $endDate = date('Y-m-d', strtotime($dateFilter)) . ' 23:59:59';
            $query->where('updated_at', '<=', $endDate);
        }

        $completedOrders = $query->with(['customer', 'inCurrency', 'outCurrency', 'currencyOrderType', 'transactions'])->get();

        // Group orders by customer and currency for easier balance calculation
        $customerCurrencyBalances = [];

        foreach ($completedOrders as $order) {
                $customerId = $order->customer_id;
                $customerName = $order->customer ? $order->customer->name : 'Unknown Customer';

            if (!isset($customerCurrencyBalances[$customerId])) {
                $customerCurrencyBalances[$customerId] = [
                        'name' => $customerName,
                    'currencies' => []
                ];
            }

            // Handle TPR orders
            if ($order->currencyOrderType->value === 'tpr' && $order->in_currency_id) {
                $fulfilledAmount = $order->transactions()
                    ->whereHas('transactionStatus', function($query) {
                        $query->where('value', '!=', 'cancelled');
                    })
                    ->whereHas('transactionType', function ($query) {
                        $query->where('value', '!=', 'bank_charges');
                    })
                    ->sum('debit');

                if ($fulfilledAmount > 0) {
                    if (!isset($customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id])) {
                        $customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id] = [
                            'currency' => $order->inCurrency,
                            'tpr_fulfilled' => 0,
                            'tpp_fulfilled' => 0,
                            'po_receivable' => 0,
                            'po_payable' => 0
                        ];
                    }
                    $customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id]['tpr_fulfilled'] += $fulfilledAmount;
                }
            }

            // Handle TPP orders
            if ($order->currencyOrderType->value === 'tpp' && $order->out_currency_id) {
                $fulfilledAmount = $order->transactions()
                    ->whereHas('transactionStatus', function($query) {
                        $query->where('value', '!=', 'cancelled');
                    })
                    ->whereHas('transactionType', function ($query) {
                        $query->where('value', '!=', 'bank_charges');
                    })
                    ->sum('credit');

                if ($fulfilledAmount > 0) {
                    if (!isset($customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id])) {
                        $customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id] = [
                            'currency' => $order->outCurrency,
                            'tpr_fulfilled' => 0,
                            'tpp_fulfilled' => 0,
                            'po_receivable' => 0,
                            'po_payable' => 0
                        ];
                    }
                    $customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id]['tpp_fulfilled'] += $fulfilledAmount;
                }
            }

            // Handle PO orders
            if ($order->currencyOrderType->value === 'po') {
                // Calculate fulfilled amounts first
                $fulfilledReceivable = $order->transactions()
                    ->whereHas('transactionStatus', function($query) {
                        $query->where('value', '!=', 'cancelled');
                    })
                    ->whereHas('transactionType', function ($query) {
                        $query->where('value', '!=', 'bank_charges');
                    })
                    ->sum('debit');

                $fulfilledPayable = $order->transactions()
                    ->whereHas('transactionStatus', function($query) {
                        $query->where('value', '!=', 'cancelled');
                    })
                    ->whereHas('transactionType', function ($query) {
                        $query->where('value', '!=', 'bank_charges');
                    })
                    ->sum('credit');

                // Handle receivable amount (in_currency)
                if ($order->in_currency_id && $order->receivable_amount > 0) {
                    $receivableBalance = $order->receivable_amount - $fulfilledReceivable;
                    if ($receivableBalance > 0) {
                        if (!isset($customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id])) {
                            $customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id] = [
                                'currency' => $order->inCurrency,
                                'tpr_fulfilled' => 0,
                                'tpp_fulfilled' => 0,
                                'po_receivable' => 0,
                                'po_payable' => 0
                            ];
                        }
                        $customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id]['po_receivable'] += $receivableBalance;
                    }
                }

                // Handle payable amount (out_currency)
                if ($order->out_currency_id && $order->payable_amount > 0) {
                    $payableBalance = $order->payable_amount - $fulfilledPayable;
                    if ($payableBalance > 0) {
                        if (!isset($customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id])) {
                            $customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id] = [
                                'currency' => $order->outCurrency,
                                'tpr_fulfilled' => 0,
                                'tpp_fulfilled' => 0,
                                'po_receivable' => 0,
                                'po_payable' => 0
                            ];
                        }
                        $customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id]['po_payable'] += $payableBalance;
                    }
                }
            }
        }

        $cashDebtors = collect();
        $coinDebtors = collect();
        $cashCreditors = collect();
        $coinCreditors = collect();

        // Process final balances
        foreach ($customerCurrencyBalances as $customerId => $customerData) {
            foreach ($customerData['currencies'] as $currencyId => $currencyData) {
                // Calculate net receivable balance (PO receivable - TPR fulfilled)
                $netReceivable = $currencyData['po_receivable'] - $currencyData['tpr_fulfilled'];

                if ($netReceivable != 0) {
                    $currencyInfo = [
                        'id' => $currencyId,
                        'code' => $currencyData['currency']->code,
                        'photo' => $currencyData['currency']->photo,
                        'name' => $currencyData['currency']->name,
                        'customers' => [[
                    'id' => $customerId,
                            'name' => $customerData['name'],
                            'amount' => $netReceivable
                        ]]
                    ];

                    if ($netReceivable > 0) {
                        if ($currencyData['currency']->is_crypto) {
                            $coinDebtors->push(array_merge($currencyInfo, ['total_amount' => $netReceivable]));
                } else {
                            $cashDebtors->push(array_merge($currencyInfo, ['total_amount' => $netReceivable]));
                        }
            } else {
                        if ($currencyData['currency']->is_crypto) {
                            $coinCreditors->push(array_merge($currencyInfo, ['total_amount' => $netReceivable]));
                        } else {
                            $cashCreditors->push(array_merge($currencyInfo, ['total_amount' => $netReceivable]));
                        }
                    }
                }

                // Calculate net payable balance (PO payable - TPP fulfilled)
                $netPayable = $currencyData['po_payable'] - $currencyData['tpp_fulfilled'];

                if ($netPayable != 0) {
                    $currencyInfo = [
                        'id' => $currencyId,
                        'code' => $currencyData['currency']->code,
                        'photo' => $currencyData['currency']->photo,
                        'name' => $currencyData['currency']->name,
                        'customers' => [[
                            'id' => $customerId,
                            'name' => $customerData['name'],
                            'amount' => -$netPayable
                        ]]
                    ];

                    if ($netPayable > 0) {
                        if ($currencyData['currency']->is_crypto) {
                            $coinCreditors->push(array_merge($currencyInfo, ['total_amount' => -$netPayable]));
                        } else {
                            $cashCreditors->push(array_merge($currencyInfo, ['total_amount' => -$netPayable]));
                        }
                    } else {
                        if ($currencyData['currency']->is_crypto) {
                            $coinDebtors->push(array_merge($currencyInfo, ['total_amount' => abs($netPayable)]));
                        } else {
                            $cashDebtors->push(array_merge($currencyInfo, ['total_amount' => abs($netPayable)]));
                        }
                    }
                }
            }
        }

        // Group by currency and sum amounts
        $cashDebtors = $this->groupByCurrency($cashDebtors);
        $cashCreditors = $this->groupByCurrency($cashCreditors);
        $coinDebtors = $this->groupByCurrency($coinDebtors);
        $coinCreditors = $this->groupByCurrency($coinCreditors);

        // Create CSV data
        $csvData = [];
        $dateInfo = $dateFilter ? 'Up to ' . date('Y-m-d', strtotime($dateFilter)) : 'All records as of ' . date('Y-m-d');
        $csvData[] = ['Debtor/Creditor Overall', $dateInfo];
        $csvData[] = [];

        // Add Cash Debtors section
        if ($cashDebtors->isNotEmpty()) {
            $csvData[] = ['CURRENCY - DEBTORS'];
            foreach ($cashDebtors as $currency) {
                $csvData[] = [
                    $currency['code'],
                    $currency['name'],
                    'Total Amount: ' . number_format(abs($currency['total_amount']), 2)
                ];
                $csvData[] = ['Customer', 'Amount'];

                foreach ($currency['customers'] as $customer) {
                    if ($customer['amount'] > 0) {
                        $csvData[] = [
                            $customer['name'],
                            number_format($customer['amount'], 2)
                        ];
                    }
                }
                $csvData[] = [];
            }
        }

        // Add Cash Creditors section
        if ($cashCreditors->isNotEmpty()) {
            $csvData[] = ['CURRENCY - CREDITORS'];
            foreach ($cashCreditors as $currency) {
                $csvData[] = [
                    $currency['code'],
                    $currency['name'],
                    'Total Amount: ' . number_format($currency['total_amount'], 2)
                ];
                $csvData[] = ['Customer', 'Amount'];

                foreach ($currency['customers'] as $customer) {
                    if ($customer['amount'] < 0) {
                        $csvData[] = [
                            $customer['name'],
                            number_format($customer['amount'], 2)
                        ];
                    }
                }
                $csvData[] = [];
            }
        }

        // Add Coin Debtors section
        if ($coinDebtors->isNotEmpty()) {
            $csvData[] = ['COIN - DEBTORS'];
            foreach ($coinDebtors as $currency) {
                $csvData[] = [
                    $currency['code'],
                    $currency['name'],
                    'Total Amount: ' . number_format(abs($currency['total_amount']), 2)
                ];
                $csvData[] = ['Customer', 'Amount'];

                foreach ($currency['customers'] as $customer) {
                    if ($customer['amount'] > 0) {
                        $csvData[] = [
                            $customer['name'],
                            number_format($customer['amount'], 2)
                        ];
                    }
                }
                $csvData[] = [];
            }
        }

        // Add Coin Creditors section
        if ($coinCreditors->isNotEmpty()) {
            $csvData[] = ['COIN - CREDITORS'];
            foreach ($coinCreditors as $currency) {
                $csvData[] = [
                    $currency['code'],
                    $currency['name'],
                    'Total Amount: ' . number_format($currency['total_amount'], 2)
                ];
                $csvData[] = ['Customer', 'Amount'];

                foreach ($currency['customers'] as $customer) {
                    if ($customer['amount'] < 0) {
                        $csvData[] = [
                            $customer['name'],
                            number_format($customer['amount'], 2)
                        ];
                    }
                }
                $csvData[] = [];
            }
        }

        // Generate CSV file
        $output = fopen('php://temp', 'w');
        foreach ($csvData as $row) {
            fputcsv($output, $row);
        }
        rewind($output);
        $csv = stream_get_contents($output);
        fclose($output);

        return Response::make($csv, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="overall-debtor-creditor' . ($dateFilter ? '-to-' . date('Y-m-d', strtotime($dateFilter)) : '') . '.csv"',
        ]);
    }

    public function exportCustomer()
    {
        $completedStatusIds = \App\Models\CurrencyOrderStatus::whereNotIn('value', ['pending', 'cancelled'])
            ->pluck('id')
            ->toArray();

        $dateFilter = request()->input('date');
        $customerNameFilter = request()->input('customer_name');
        $customerCodeFilter = request()->input('customer_code');

        $query = \App\Models\CurrencyOrder::whereIn('currency_order_status_id', $completedStatusIds)
            ->whereHas('currencyOrderType', function($query) {
                $query->whereIn('value', ['po', 'tpp', 'tpr']);
            });

        if ($dateFilter) {
            $endDate = date('Y-m-d', strtotime($dateFilter)) . ' 23:59:59';
            $query->where('updated_at', '<=', $endDate);
        }

        if ($customerNameFilter || $customerCodeFilter) {
            $query->whereHas('customer', function($q) use ($customerNameFilter, $customerCodeFilter) {
                if ($customerNameFilter) {
                    $q->where('name', 'like', '%' . $customerNameFilter . '%');
                }
                if ($customerCodeFilter) {
                    $q->where('code', 'like', '%' . $customerCodeFilter . '%');
                }
            });
        }

        $completedOrders = $query->with(['customer', 'inCurrency', 'outCurrency', 'currencyOrderType', 'transactions'])->get();

        // Group orders by customer and currency for easier balance calculation
        $customerCurrencyBalances = [];

        foreach ($completedOrders as $order) {
            $customerId = $order->customer_id;
            $customerName = $order->customer ? $order->customer->name : 'Unknown Customer';
            $customerCode = $order->customer ? $order->customer->code : '';

            if (!isset($customerCurrencyBalances[$customerId])) {
                $customerCurrencyBalances[$customerId] = [
                    'id' => $customerId,
                    'name' => $customerName,
                    'code' => $customerCode,
                    'currencies' => []
                ];
            }

            // Handle TPR orders
            if ($order->currencyOrderType->value === 'tpr' && $order->in_currency_id) {
                $fulfilledAmount = $order->transactions()
                    ->whereHas('transactionStatus', function($query) {
                        $query->where('value', '!=', 'cancelled');
                    })
                    ->whereHas('transactionType', function ($query) {
                        $query->where('value', '!=', 'bank_charges');
                    })
                    ->sum('debit');

                if ($fulfilledAmount > 0) {
                    if (!isset($customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id])) {
                        $customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id] = [
                            'tpr_fulfilled' => 0,
                            'tpp_fulfilled' => 0,
                            'po_receivable' => 0,
                            'po_payable' => 0
                        ];
                    }
                    $customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id]['tpr_fulfilled'] += $fulfilledAmount;
                }
            }

            // Handle TPP orders
            if ($order->currencyOrderType->value === 'tpp' && $order->out_currency_id) {
                $fulfilledAmount = $order->transactions()
                    ->whereHas('transactionStatus', function($query) {
                        $query->where('value', '!=', 'cancelled');
                    })
                    ->whereHas('transactionType', function ($query) {
                        $query->where('value', '!=', 'bank_charges');
                    })
                    ->sum('credit');

                if ($fulfilledAmount > 0) {
                    if (!isset($customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id])) {
                        $customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id] = [
                            'tpr_fulfilled' => 0,
                            'tpp_fulfilled' => 0,
                            'po_receivable' => 0,
                            'po_payable' => 0
                        ];
                    }
                    $customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id]['tpp_fulfilled'] += $fulfilledAmount;
                }
            }

            // Handle PO orders
            if ($order->currencyOrderType->value === 'po') {
                // Calculate fulfilled amounts first
                $fulfilledReceivable = $order->transactions()
                    ->whereHas('transactionStatus', function($query) {
                        $query->where('value', '!=', 'cancelled');
                    })
                    ->whereHas('transactionType', function ($query) {
                        $query->where('value', '!=', 'bank_charges');
                    })
                    ->sum('debit');

                $fulfilledPayable = $order->transactions()
                    ->whereHas('transactionStatus', function($query) {
                        $query->where('value', '!=', 'cancelled');
                    })
                    ->whereHas('transactionType', function ($query) {
                        $query->where('value', '!=', 'bank_charges');
                    })
                    ->sum('credit');

                // Handle receivable amount (in_currency)
                if ($order->in_currency_id && $order->receivable_amount > 0) {
                    $receivableBalance = $order->receivable_amount - $fulfilledReceivable;
                    if ($receivableBalance > 0) {
                        if (!isset($customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id])) {
                            $customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id] = [
                                'tpr_fulfilled' => 0,
                                'tpp_fulfilled' => 0,
                                'po_receivable' => 0,
                                'po_payable' => 0
                            ];
                        }
                        $customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id]['po_receivable'] += $receivableBalance;
                    }
                }

                // Handle payable amount (out_currency)
                if ($order->out_currency_id && $order->payable_amount > 0) {
                    $payableBalance = $order->payable_amount - $fulfilledPayable;
                    if ($payableBalance > 0) {
                        if (!isset($customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id])) {
                            $customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id] = [
                                'tpr_fulfilled' => 0,
                                'tpp_fulfilled' => 0,
                                'po_receivable' => 0,
                                'po_payable' => 0
                            ];
                        }
                        $customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id]['po_payable'] += $payableBalance;
                    }
                }
            }
        }

        // Process final balances
        $customers = [];
        foreach ($customerCurrencyBalances as $customerId => $customerData) {
            $customer = [
                'id' => $customerId,
                'name' => $customerData['name'],
                'code' => $customerData['code'],
                'currencies' => []
            ];

            foreach ($customerData['currencies'] as $currencyId => $currencyData) {
                // Calculate net receivable (PO receivable - TPR fulfilled)
                $netReceivable = $currencyData['po_receivable'] - $currencyData['tpr_fulfilled'];

                // Calculate net payable (PO payable - TPP fulfilled)
                $netPayable = $currencyData['po_payable'] - $currencyData['tpp_fulfilled'];

                // Final balance for this currency
                $finalBalance = $netReceivable - $netPayable;

                if ($finalBalance != 0) {
                    $customer['currencies'][$currencyId] = $finalBalance;
                }
            }

            if (!empty($customer['currencies'])) {
                $customers[] = $customer;
            }
        }

        // Get all unique currencies used
        $currenciesUsed = collect();
        foreach ($completedOrders as $order) {
            if ($order->in_currency_id && !$currenciesUsed->contains('id', $order->in_currency_id)) {
                $currenciesUsed->push([
                    'id' => $order->in_currency_id,
                    'code' => $order->inCurrency->code,
                    'name' => $order->inCurrency->name,
                    'photo' => $order->inCurrency->photo,
                    'is_crypto' => $order->inCurrency->is_crypto,
                ]);
            }

            if ($order->out_currency_id && !$currenciesUsed->contains('id', $order->out_currency_id)) {
                $currenciesUsed->push([
                    'id' => $order->out_currency_id,
                    'code' => $order->outCurrency->code,
                    'name' => $order->outCurrency->name,
                    'photo' => $order->outCurrency->photo,
                    'is_crypto' => $order->outCurrency->is_crypto,
                ]);
            }
        }

        $currenciesUsed = $currenciesUsed->sort(function($a, $b) {
            if ($a['is_crypto'] === $b['is_crypto']) {
                return strcmp($a['code'], $b['code']);
            }
            return $a['is_crypto'] ? 1 : -1;
        })->values();

        // Calculate totals for each currency
        $totals = [];
        foreach ($currenciesUsed as $currency) {
            $totals[$currency['id']] = 0;
        }

        foreach ($customers as $customer) {
            foreach ($customer['currencies'] as $currencyId => $amount) {
                if (isset($totals[$currencyId])) {
                    $totals[$currencyId] += $amount;
                }
            }
        }

        // Create CSV data
        $csvData = [];
        $dateInfo = $dateFilter ? 'Up to ' . date('Y-m-d', strtotime($dateFilter)) : 'All records as of ' . date('Y-m-d');
        $csvData[] = ['Debtor/Creditor By Customer', $dateInfo];
        $csvData[] = [];

        // Header row with currency codes
        $headerRow = ['Customer Name', 'Customer Code'];
        foreach ($currenciesUsed as $currency) {
            $headerRow[] = $currency['code'];
        }
        $csvData[] = $headerRow;

        // Sort customers by name
        usort($customers, function($a, $b) {
            return strcmp($a['name'], $b['name']);
        });

        // Data rows for each customer
        foreach ($customers as $customer) {
            $row = [$customer['name'], $customer['code']];

            foreach ($currenciesUsed as $currency) {
                $balance = isset($customer['currencies'][$currency['id']]) ? $customer['currencies'][$currency['id']] : 0;
                if ($balance == 0) {
                    $row[] = '-';
                } else {
                    $prefix = $balance < 0 ? '-' : '';
                    $row[] = $prefix . number_format(abs($balance), 2);
                }
            }

            $csvData[] = $row;
        }

        // Add totals row
        $totalsRow = ['Total', ''];
        foreach ($currenciesUsed as $currency) {
            $total = $totals[$currency['id']];
            if ($total == 0) {
                $totalsRow[] = '-';
            } else {
                $prefix = $total < 0 ? '-' : '';
                $totalsRow[] = $prefix . number_format(abs($total), 2);
            }
        }
        $csvData[] = $totalsRow;

        // Generate CSV file
        $output = fopen('php://temp', 'w');
        foreach ($csvData as $row) {
            fputcsv($output, $row);
        }
        rewind($output);
        $csv = stream_get_contents($output);
        fclose($output);

        // Set the headers for download
        $filename = 'customer-debtor-creditor';
        if ($dateFilter) {
            $filename .= '-to-' . date('Y-m-d', strtotime($dateFilter));
        }
        if ($customerNameFilter) {
            $filename .= '-' . strtolower(str_replace(' ', '-', $customerNameFilter));
        }
        $filename .= '.csv';

        return Response::make($csv, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }

    private function isCoinCurrency($code)
    {
        $currency = \App\Models\Currency::where('code', $code)->first();
        return $currency && $currency->is_crypto;
    }

    private function groupByCurrency($collection)
    {
        return $collection->groupBy('id')->map(function ($group) {
            $first = $group->first();
            $totalAmount = 0;
            $customers = [];

            // Combine customer amounts for the same currency
            foreach ($group as $item) {
                foreach ($item['customers'] as $customer) {
                    $customerId = $customer['id'];
                    if (!isset($customers[$customerId])) {
                        $customers[$customerId] = [
                            'id' => $customer['id'],
                            'name' => $customer['name'],
                            'amount' => 0
                        ];
                    }
                    $customers[$customerId]['amount'] += $customer['amount'];
                    $totalAmount += $customer['amount'];
                }
            }

            return [
                'id' => $first['id'],
                'code' => $first['code'],
                'photo' => $first['photo'],
                'name' => $first['name'],
                'total_amount' => $totalAmount,
                'customers' => array_values($customers)
            ];
        })->values();
    }

    private function processOrdersChunk($completedOrders, &$customerCurrencyBalances)
    {
        foreach ($completedOrders as $order) {
            $customerId = $order->customer_id;
            $customerName = $order->customer ? $order->customer->name : 'Unknown Customer';

            if (!isset($customerCurrencyBalances[$customerId])) {
                $customerCurrencyBalances[$customerId] = [
                    'name' => $customerName,
                    'currencies' => []
                ];
            }

            // Handle TPR orders
            if ($order->currencyOrderType->value === 'tpr' && $order->in_currency_id) {
                $fulfilledAmount = $order->transactions()
                    ->whereHas('transactionStatus', function($query) {
                        $query->where('value', '!=', 'cancelled');
                    })
                    ->whereHas('transactionType', function ($query) {
                        $query->where('value', '!=', 'bank_charges');
                    })
                    ->sum('debit');

                if ($fulfilledAmount > 0) {
                    if (!isset($customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id])) {
                        $customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id] = [
                            'currency' => $order->inCurrency,
                            'tpr_fulfilled' => 0,
                            'tpp_fulfilled' => 0,
                            'po_receivable' => 0,
                            'po_payable' => 0
                        ];
                    }
                    $customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id]['tpr_fulfilled'] += $fulfilledAmount;
                }
            }

            // Handle TPP orders
            if ($order->currencyOrderType->value === 'tpp' && $order->out_currency_id) {
                $fulfilledAmount = $order->transactions()
                    ->whereHas('transactionStatus', function($query) {
                        $query->where('value', '!=', 'cancelled');
                    })
                    ->whereHas('transactionType', function ($query) {
                        $query->where('value', '!=', 'bank_charges');
                    })
                    ->sum('credit');

                if ($fulfilledAmount > 0) {
                    if (!isset($customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id])) {
                        $customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id] = [
                            'currency' => $order->outCurrency,
                            'tpr_fulfilled' => 0,
                            'tpp_fulfilled' => 0,
                            'po_receivable' => 0,
                            'po_payable' => 0
                        ];
                    }
                    $customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id]['tpp_fulfilled'] += $fulfilledAmount;
                }
            }

            // Handle PO orders
            if ($order->currencyOrderType->value === 'po') {
                // Calculate fulfilled amounts first
                $fulfilledReceivable = $order->transactions()
                    ->whereHas('transactionStatus', function($query) {
                        $query->where('value', '!=', 'cancelled');
                    })
                    ->whereHas('transactionType', function ($query) {
                        $query->where('value', '!=', 'bank_charges');
                    })
                    ->sum('debit');

                $fulfilledPayable = $order->transactions()
                    ->whereHas('transactionStatus', function($query) {
                        $query->where('value', '!=', 'cancelled');
                    })
                    ->whereHas('transactionType', function ($query) {
                        $query->where('value', '!=', 'bank_charges');
                    })
                    ->sum('credit');

                // Handle receivable amount (in_currency)
                if ($order->in_currency_id && $order->receivable_amount > 0) {
                    $receivableBalance = $order->receivable_amount - $fulfilledReceivable;
                    if ($receivableBalance > 0) {
                        if (!isset($customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id])) {
                            $customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id] = [
                                'currency' => $order->inCurrency,
                                'tpr_fulfilled' => 0,
                                'tpp_fulfilled' => 0,
                                'po_receivable' => 0,
                                'po_payable' => 0
                            ];
                        }
                        $customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id]['po_receivable'] += $receivableBalance;
                    }
                }

                // Handle payable amount (out_currency)
                if ($order->out_currency_id && $order->payable_amount > 0) {
                    $payableBalance = $order->payable_amount - $fulfilledPayable;
                    if ($payableBalance > 0) {
                        if (!isset($customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id])) {
                            $customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id] = [
                                'currency' => $order->outCurrency,
                                'tpr_fulfilled' => 0,
                                'tpp_fulfilled' => 0,
                                'po_receivable' => 0,
                                'po_payable' => 0
                            ];
                        }
                        $customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id]['po_payable'] += $payableBalance;
                    }
                }
            }
        }
    }

    private function processCustomerOrdersChunk($completedOrders, &$customerCurrencyBalances, &$currenciesUsed)
    {
        foreach ($completedOrders as $order) {
            $customerId = $order->customer_id;
            $customerName = $order->customer ? $order->customer->name : 'Unknown Customer';
            $customerCode = $order->customer ? $order->customer->code : '';

            if (!isset($customerCurrencyBalances[$customerId])) {
                $customerCurrencyBalances[$customerId] = [
                    'id' => $customerId,
                    'name' => $customerName,
                    'code' => $customerCode,
                    'currencies' => []
                ];
            }

            // Collect unique currencies
            if ($order->in_currency_id && !$currenciesUsed->contains('id', $order->in_currency_id)) {
                $currenciesUsed->push([
                    'id' => $order->in_currency_id,
                    'code' => $order->inCurrency->code,
                    'name' => $order->inCurrency->name,
                    'photo' => $order->inCurrency->photo,
                    'is_crypto' => $order->inCurrency->is_crypto,
                ]);
            }

            if ($order->out_currency_id && !$currenciesUsed->contains('id', $order->out_currency_id)) {
                $currenciesUsed->push([
                    'id' => $order->out_currency_id,
                    'code' => $order->outCurrency->code,
                    'name' => $order->outCurrency->name,
                    'photo' => $order->outCurrency->photo,
                    'is_crypto' => $order->outCurrency->is_crypto,
                ]);
            }

            // Handle TPR orders
            if ($order->currencyOrderType->value === 'tpr' && $order->in_currency_id) {
                $fulfilledAmount = $order->transactions()
                    ->whereHas('transactionStatus', function($query) {
                        $query->where('value', '!=', 'cancelled');
                    })
                    ->whereHas('transactionType', function ($query) {
                        $query->where('value', '!=', 'bank_charges');
                    })
                    ->sum('debit');

                if ($fulfilledAmount > 0) {
                    if (!isset($customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id])) {
                        $customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id] = [
                            'tpr_fulfilled' => 0,
                            'tpp_fulfilled' => 0,
                            'po_receivable' => 0,
                            'po_payable' => 0
                        ];
                    }
                    $customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id]['tpr_fulfilled'] += $fulfilledAmount;
                }
            }

            // Handle TPP orders
            if ($order->currencyOrderType->value === 'tpp' && $order->out_currency_id) {
                $fulfilledAmount = $order->transactions()
                    ->whereHas('transactionStatus', function($query) {
                        $query->where('value', '!=', 'cancelled');
                    })
                    ->whereHas('transactionType', function ($query) {
                        $query->where('value', '!=', 'bank_charges');
                    })
                    ->sum('credit');

                if ($fulfilledAmount > 0) {
                    if (!isset($customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id])) {
                        $customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id] = [
                            'tpr_fulfilled' => 0,
                            'tpp_fulfilled' => 0,
                            'po_receivable' => 0,
                            'po_payable' => 0
                        ];
                    }
                    $customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id]['tpp_fulfilled'] += $fulfilledAmount;
                }
            }

            // Handle PO orders
            if ($order->currencyOrderType->value === 'po') {
                // Calculate fulfilled amounts first
                $fulfilledReceivable = $order->transactions()
                    ->whereHas('transactionStatus', function($query) {
                        $query->where('value', '!=', 'cancelled');
                    })
                    ->whereHas('transactionType', function ($query) {
                        $query->where('value', '!=', 'bank_charges');
                    })
                    ->sum('debit');

                $fulfilledPayable = $order->transactions()
                    ->whereHas('transactionStatus', function($query) {
                        $query->where('value', '!=', 'cancelled');
                    })
                    ->whereHas('transactionType', function ($query) {
                        $query->where('value', '!=', 'bank_charges');
                    })
                    ->sum('credit');

                // Handle receivable amount (in_currency)
                if ($order->in_currency_id && $order->receivable_amount > 0) {
                    $receivableBalance = $order->receivable_amount - $fulfilledReceivable;
                    if ($receivableBalance > 0) {
                        if (!isset($customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id])) {
                            $customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id] = [
                                'tpr_fulfilled' => 0,
                                'tpp_fulfilled' => 0,
                                'po_receivable' => 0,
                                'po_payable' => 0
                            ];
                        }
                        $customerCurrencyBalances[$customerId]['currencies'][$order->in_currency_id]['po_receivable'] += $receivableBalance;
                    }
                }

                // Handle payable amount (out_currency)
                if ($order->out_currency_id && $order->payable_amount > 0) {
                    $payableBalance = $order->payable_amount - $fulfilledPayable;
                    if ($payableBalance > 0) {
                        if (!isset($customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id])) {
                            $customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id] = [
                                'tpr_fulfilled' => 0,
                                'tpp_fulfilled' => 0,
                                'po_receivable' => 0,
                                'po_payable' => 0
                            ];
                        }
                        $customerCurrencyBalances[$customerId]['currencies'][$order->out_currency_id]['po_payable'] += $payableBalance;
                    }
                }
            }
        }
    }

    private function getOptimizedBalances($dateFilter = null)
    {
        // Get base query with date filter
        $baseQuery = \App\Models\CurrencyOrder::query()
            ->whereHas('currencyOrderStatus', function($query) {
                $query->whereNotIn('value', ['pending', 'cancelled']);
            })
            ->whereHas('currencyOrderType', function($query) {
                $query->whereIn('value', ['po', 'tpp', 'tpr']);
            });

        if ($dateFilter) {
            $endDate = date('Y-m-d', strtotime($dateFilter)) . ' 23:59:59';
            $baseQuery->where('updated_at', '<=', $endDate);
        }

        // Get aggregated transaction data using raw SQL for performance
        $results = DB::select("
            SELECT
                co.customer_id,
                c.name as customer_name,
                co.in_currency_id,
                co.out_currency_id,
                cot.value as order_type,
                co.receivable_amount,
                co.payable_amount,
                COALESCE(SUM(CASE
                    WHEN t.debit > 0 AND ts.value != 'cancelled' AND tt.value != 'bank_charges'
                    THEN t.debit ELSE 0 END), 0) as total_debit,
                COALESCE(SUM(CASE
                    WHEN t.credit > 0 AND ts.value != 'cancelled' AND tt.value != 'bank_charges'
                    THEN t.credit ELSE 0 END), 0) as total_credit
            FROM currency_orders co
            INNER JOIN currency_order_statuses cos ON co.currency_order_status_id = cos.id
            INNER JOIN currency_order_types cot ON co.currency_order_type_id = cot.id
            LEFT JOIN customers c ON co.customer_id = c.id
            LEFT JOIN transactions t ON co.id = t.currency_order_id
            LEFT JOIN transaction_statuses ts ON t.transaction_status_id = ts.id
            LEFT JOIN transaction_types tt ON t.transaction_type_id = tt.id
            WHERE cos.value NOT IN ('pending', 'cancelled')
            AND cot.value IN ('po', 'tpp', 'tpr')
            " . ($dateFilter ? "AND co.updated_at <= ?" : "") . "
            GROUP BY co.id, co.customer_id, c.name, co.in_currency_id, co.out_currency_id,
                     cot.value, co.receivable_amount, co.payable_amount
        ", $dateFilter ? [$endDate] : []);

        // Process results into the expected format
        $customerCurrencyBalances = [];

        foreach ($results as $row) {
            $customerId = $row->customer_id;
            $customerName = $row->customer_name ?: 'Unknown Customer';

            if (!isset($customerCurrencyBalances[$customerId])) {
                $customerCurrencyBalances[$customerId] = [
                    'name' => $customerName,
                    'currencies' => []
                ];
            }

            // Handle different order types
            if ($row->order_type === 'tpr' && $row->in_currency_id) {
                $this->addCurrencyBalance($customerCurrencyBalances, $customerId, $row->in_currency_id, 'tpr_fulfilled', $row->total_debit);
            } elseif ($row->order_type === 'tpp' && $row->out_currency_id) {
                $this->addCurrencyBalance($customerCurrencyBalances, $customerId, $row->out_currency_id, 'tpp_fulfilled', $row->total_credit);
            } elseif ($row->order_type === 'po') {
                if ($row->in_currency_id && $row->receivable_amount > 0) {
                    $receivableBalance = $row->receivable_amount - $row->total_debit;
                    if ($receivableBalance > 0) {
                        $this->addCurrencyBalance($customerCurrencyBalances, $customerId, $row->in_currency_id, 'po_receivable', $receivableBalance);
                    }
                }
                if ($row->out_currency_id && $row->payable_amount > 0) {
                    $payableBalance = $row->payable_amount - $row->total_credit;
                    if ($payableBalance > 0) {
                        $this->addCurrencyBalance($customerCurrencyBalances, $customerId, $row->out_currency_id, 'po_payable', $payableBalance);
                    }
                }
            }
        }

        return $customerCurrencyBalances;
    }

    private function addCurrencyBalance(&$customerCurrencyBalances, $customerId, $currencyId, $type, $amount)
    {
        if (!isset($customerCurrencyBalances[$customerId]['currencies'][$currencyId])) {
            // Get currency info
            $currency = \App\Models\Currency::find($currencyId);
            $customerCurrencyBalances[$customerId]['currencies'][$currencyId] = [
                'currency' => $currency,
                'tpr_fulfilled' => 0,
                'tpp_fulfilled' => 0,
                'po_receivable' => 0,
                'po_payable' => 0
            ];
        }
        $customerCurrencyBalances[$customerId]['currencies'][$currencyId][$type] += $amount;
    }

    private function getOptimizedCustomerBalances($dateFilter = null, $customerNameFilter = null, $customerCodeFilter = null)
    {
        // Build WHERE conditions
        $whereConditions = "cos.value NOT IN ('pending', 'cancelled') AND cot.value IN ('po', 'tpp', 'tpr')";
        $params = [];

        if ($dateFilter) {
            $endDate = date('Y-m-d', strtotime($dateFilter)) . ' 23:59:59';
            $whereConditions .= " AND co.updated_at <= ?";
            $params[] = $endDate;
        }

        if ($customerNameFilter) {
            $whereConditions .= " AND c.name LIKE ?";
            $params[] = '%' . $customerNameFilter . '%';
        }

        if ($customerCodeFilter) {
            $whereConditions .= " AND c.code LIKE ?";
            $params[] = '%' . $customerCodeFilter . '%';
        }

        // Get aggregated data with customer filters
        $results = DB::select("
            SELECT
                co.customer_id,
                c.name as customer_name,
                c.code as customer_code,
                co.in_currency_id,
                co.out_currency_id,
                cot.value as order_type,
                co.receivable_amount,
                co.payable_amount,
                COALESCE(SUM(CASE
                    WHEN t.debit > 0 AND ts.value != 'cancelled' AND tt.value != 'bank_charges'
                    THEN t.debit ELSE 0 END), 0) as total_debit,
                COALESCE(SUM(CASE
                    WHEN t.credit > 0 AND ts.value != 'cancelled' AND tt.value != 'bank_charges'
                    THEN t.credit ELSE 0 END), 0) as total_credit
            FROM currency_orders co
            INNER JOIN currency_order_statuses cos ON co.currency_order_status_id = cos.id
            INNER JOIN currency_order_types cot ON co.currency_order_type_id = cot.id
            LEFT JOIN customers c ON co.customer_id = c.id
            LEFT JOIN transactions t ON co.id = t.currency_order_id
            LEFT JOIN transaction_statuses ts ON t.transaction_status_id = ts.id
            LEFT JOIN transaction_types tt ON t.transaction_type_id = tt.id
            WHERE {$whereConditions}
            GROUP BY co.id, co.customer_id, c.name, c.code, co.in_currency_id, co.out_currency_id,
                     cot.value, co.receivable_amount, co.payable_amount
        ", $params);

        // Process results for customer view
        $customerCurrencyBalances = [];

        foreach ($results as $row) {
            $customerId = $row->customer_id;
            $customerName = $row->customer_name ?: 'Unknown Customer';
            $customerCode = $row->customer_code ?: '';

            if (!isset($customerCurrencyBalances[$customerId])) {
                $customerCurrencyBalances[$customerId] = [
                    'id' => $customerId,
                    'name' => $customerName,
                    'code' => $customerCode,
                    'currencies' => []
                ];
            }

            // Handle different order types
            if ($row->order_type === 'tpr' && $row->in_currency_id) {
                $this->addCustomerCurrencyBalance($customerCurrencyBalances, $customerId, $row->in_currency_id, 'tpr_fulfilled', $row->total_debit);
            } elseif ($row->order_type === 'tpp' && $row->out_currency_id) {
                $this->addCustomerCurrencyBalance($customerCurrencyBalances, $customerId, $row->out_currency_id, 'tpp_fulfilled', $row->total_credit);
            } elseif ($row->order_type === 'po') {
                if ($row->in_currency_id && $row->receivable_amount > 0) {
                    $receivableBalance = $row->receivable_amount - $row->total_debit;
                    if ($receivableBalance > 0) {
                        $this->addCustomerCurrencyBalance($customerCurrencyBalances, $customerId, $row->in_currency_id, 'po_receivable', $receivableBalance);
                    }
                }
                if ($row->out_currency_id && $row->payable_amount > 0) {
                    $payableBalance = $row->payable_amount - $row->total_credit;
                    if ($payableBalance > 0) {
                        $this->addCustomerCurrencyBalance($customerCurrencyBalances, $customerId, $row->out_currency_id, 'po_payable', $payableBalance);
                    }
                }
            }
        }

        return $customerCurrencyBalances;
    }

    private function addCustomerCurrencyBalance(&$customerCurrencyBalances, $customerId, $currencyId, $type, $amount)
    {
        if (!isset($customerCurrencyBalances[$customerId]['currencies'][$currencyId])) {
            $customerCurrencyBalances[$customerId]['currencies'][$currencyId] = [
                'tpr_fulfilled' => 0,
                'tpp_fulfilled' => 0,
                'po_receivable' => 0,
                'po_payable' => 0
            ];
        }
        $customerCurrencyBalances[$customerId]['currencies'][$currencyId][$type] += $amount;
    }

    private function getUsedCurrencies($dateFilter = null, $customerNameFilter = null, $customerCodeFilter = null)
    {
        // Build WHERE conditions
        $whereConditions = "cos.value NOT IN ('pending', 'cancelled') AND cot.value IN ('po', 'tpp', 'tpr')";
        $params = [];

        if ($dateFilter) {
            $endDate = date('Y-m-d', strtotime($dateFilter)) . ' 23:59:59';
            $whereConditions .= " AND co.updated_at <= ?";
            $params[] = $endDate;
        }

        if ($customerNameFilter) {
            $whereConditions .= " AND c.name LIKE ?";
            $params[] = '%' . $customerNameFilter . '%';
        }

        if ($customerCodeFilter) {
            $whereConditions .= " AND c.code LIKE ?";
            $params[] = '%' . $customerCodeFilter . '%';
        }

        // Get unique currencies used
        $currencies = DB::select("
            SELECT DISTINCT
                curr.id,
                curr.code,
                curr.name,
                curr.photo_path,
                curr.is_crypto
            FROM currency_orders co
            INNER JOIN currency_order_statuses cos ON co.currency_order_status_id = cos.id
            INNER JOIN currency_order_types cot ON co.currency_order_type_id = cot.id
            LEFT JOIN customers c ON co.customer_id = c.id
            LEFT JOIN currencies curr ON (curr.id = co.in_currency_id OR curr.id = co.out_currency_id)
            WHERE {$whereConditions}
            AND curr.id IS NOT NULL
            ORDER BY curr.is_crypto ASC, curr.code ASC
        ", $params);

        // Transform the results to include the photo URL using the Currency model accessor logic
        return collect($currencies)->map(function ($currency) {
            $photoUrl = $currency->photo_path
                ? \Illuminate\Support\Facades\URL::route('image', [
                    'path' => $currency->photo_path,
                    'w' => 40,
                    'h' => 40,
                    'fit' => 'crop',
                ])
                : '/images/currencies/BLANK.png';

            return [
                'id' => $currency->id,
                'code' => $currency->code,
                'name' => $currency->name,
                'photo' => $photoUrl,
                'is_crypto' => (bool) $currency->is_crypto,
            ];
        });
    }

    private function getOptimizedDailyBalances($startDate, $endDate)
    {
        // Get aggregated transaction data using raw SQL for performance
        $results = DB::select("
            SELECT
                co.customer_id,
                c.name as customer_name,
                co.in_currency_id,
                co.out_currency_id,
                cot.value as order_type,
                co.receivable_amount,
                co.payable_amount,
                COALESCE(SUM(CASE
                    WHEN t.debit > 0 AND ts.value != 'cancelled' AND tt.value != 'bank_charges'
                    THEN t.debit ELSE 0 END), 0) as total_debit,
                COALESCE(SUM(CASE
                    WHEN t.credit > 0 AND ts.value != 'cancelled' AND tt.value != 'bank_charges'
                    THEN t.credit ELSE 0 END), 0) as total_credit
            FROM currency_orders co
            INNER JOIN currency_order_statuses cos ON co.currency_order_status_id = cos.id
            INNER JOIN currency_order_types cot ON co.currency_order_type_id = cot.id
            LEFT JOIN customers c ON co.customer_id = c.id
            LEFT JOIN transactions t ON co.id = t.currency_order_id
            LEFT JOIN transaction_statuses ts ON t.transaction_status_id = ts.id
            LEFT JOIN transaction_types tt ON t.transaction_type_id = tt.id
            WHERE cos.value NOT IN ('pending', 'cancelled')
            AND cot.value IN ('po', 'tpp', 'tpr')
            AND co.updated_at BETWEEN ? AND ?
            GROUP BY co.id, co.customer_id, c.name, co.in_currency_id, co.out_currency_id,
                     cot.value, co.receivable_amount, co.payable_amount
        ", [$startDate, $endDate]);

        // Process results into the expected format
        $customerCurrencyBalances = [];

        foreach ($results as $row) {
            $customerId = $row->customer_id;
            $customerName = $row->customer_name ?: 'Unknown Customer';

            if (!isset($customerCurrencyBalances[$customerId])) {
                $customerCurrencyBalances[$customerId] = [
                    'name' => $customerName,
                    'currencies' => []
                ];
            }

            // Handle different order types
            if ($row->order_type === 'tpr' && $row->in_currency_id) {
                $this->addDailyCurrencyBalance($customerCurrencyBalances, $customerId, $row->in_currency_id, 'tpr_fulfilled', $row->total_debit);
            } elseif ($row->order_type === 'tpp' && $row->out_currency_id) {
                $this->addDailyCurrencyBalance($customerCurrencyBalances, $customerId, $row->out_currency_id, 'tpp_fulfilled', $row->total_credit);
            } elseif ($row->order_type === 'po') {
                if ($row->in_currency_id && $row->receivable_amount > 0) {
                    $receivableBalance = $row->receivable_amount - $row->total_debit;
                    if ($receivableBalance > 0) {
                        $this->addDailyCurrencyBalance($customerCurrencyBalances, $customerId, $row->in_currency_id, 'po_receivable', $receivableBalance);
                    }
                }
                if ($row->out_currency_id && $row->payable_amount > 0) {
                    $payableBalance = $row->payable_amount - $row->total_credit;
                    if ($payableBalance > 0) {
                        $this->addDailyCurrencyBalance($customerCurrencyBalances, $customerId, $row->out_currency_id, 'po_payable', $payableBalance);
                    }
                }
            }
        }

        return $customerCurrencyBalances;
    }

    private function addDailyCurrencyBalance(&$customerCurrencyBalances, $customerId, $currencyId, $type, $amount)
    {
        if (!isset($customerCurrencyBalances[$customerId]['currencies'][$currencyId])) {
            // Get currency info
            $currency = \App\Models\Currency::find($currencyId);
            $customerCurrencyBalances[$customerId]['currencies'][$currencyId] = [
                'currency' => $currency,
                'tpr_fulfilled' => 0,
                'tpp_fulfilled' => 0,
                'po_receivable' => 0,
                'po_payable' => 0
            ];
        }
        $customerCurrencyBalances[$customerId]['currencies'][$currencyId][$type] += $amount;
    }
}
