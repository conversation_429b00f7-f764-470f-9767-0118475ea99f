{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "ext-exif": "*", "ext-gd": "*", "barryvdh/laravel-dompdf": "^3.1", "fakerphp/faker": "^1.23", "guzzlehttp/guzzle": "^7.2", "inertiajs/inertia-laravel": "^1.0", "laravel/framework": "^11.1", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "league/glide-symfony": "^2.0", "maatwebsite/excel": "^3.1", "owen-it/laravel-auditing": "^13.6", "rappasoft/laravel-authentication-log": "^5.0", "spatie/laravel-permission": "^6.10", "tightenco/ziggy": "^2.4", "torann/geoip": "^3.0"}, "require-dev": {"larastan/larastan": "^2.8", "laravel/pint": "^1.21", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0", "roave/security-advisories": "dev-latest", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "compile": ["@php artisan migrate:fresh --seed"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}